{"version": 2, "from_file_path": "/Users/<USER>/.move/https___github_com_MystenLabs_sui_git_b448b1d971bd6c1aac8ef4eee4305943806d5d5b/crates/sui-framework/packages/sui-system/sources/validator_wrapper.move", "definition_location": {"file_hash": [122, 204, 7, 1, 205, 54, 3, 51, 125, 214, 94, 62, 42, 78, 68, 201, 176, 15, 130, 139, 9, 152, 202, 13, 253, 110, 176, 140, 177, 223, 120, 245], "start": 94, "end": 111}, "module_name": ["0000000000000000000000000000000000000000000000000000000000000003", "validator_wrapper"], "struct_map": {"0": {"definition_location": {"file_hash": [122, 204, 7, 1, 205, 54, 3, 51, 125, 214, 94, 62, 42, 78, 68, 201, 176, 15, 130, 139, 9, 152, 202, 13, 253, 110, 176, 140, 177, 223, 120, 245], "start": 239, "end": 255}, "type_parameters": [], "fields": [{"file_hash": [122, 204, 7, 1, 205, 54, 3, 51, 125, 214, 94, 62, 42, 78, 68, 201, 176, 15, 130, 139, 9, 152, 202, 13, 253, 110, 176, 140, 177, 223, 120, 245], "start": 272, "end": 277}]}}, "enum_map": {}, "function_map": {"0": {"location": {"file_hash": [122, 204, 7, 1, 205, 54, 3, 51, 125, 214, 94, 62, 42, 78, 68, 201, 176, 15, 130, 139, 9, 152, 202, 13, 253, 110, 176, 140, 177, 223, 120, 245], "start": 332, "end": 508}, "definition_location": {"file_hash": [122, 204, 7, 1, 205, 54, 3, 51, 125, 214, 94, 62, 42, 78, 68, 201, 176, 15, 130, 139, 9, 152, 202, 13, 253, 110, 176, 140, 177, 223, 120, 245], "start": 352, "end": 361}, "type_parameters": [], "parameters": [["validator#0#0", {"file_hash": [122, 204, 7, 1, 205, 54, 3, 51, 125, 214, 94, 62, 42, 78, 68, 201, 176, 15, 130, 139, 9, 152, 202, 13, 253, 110, 176, 140, 177, 223, 120, 245], "start": 362, "end": 371}], ["ctx#0#0", {"file_hash": [122, 204, 7, 1, 205, 54, 3, 51, 125, 214, 94, 62, 42, 78, 68, 201, 176, 15, 130, 139, 9, 152, 202, 13, 253, 110, 176, 140, 177, 223, 120, 245], "start": 384, "end": 387}]], "returns": [{"file_hash": [122, 204, 7, 1, 205, 54, 3, 51, 125, 214, 94, 62, 42, 78, 68, 201, 176, 15, 130, 139, 9, 152, 202, 13, 253, 110, 176, 140, 177, 223, 120, 245], "start": 406, "end": 422}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [122, 204, 7, 1, 205, 54, 3, 51, 125, 214, 94, 62, 42, 78, 68, 201, 176, 15, 130, 139, 9, 152, 202, 13, 253, 110, 176, 140, 177, 223, 120, 245], "start": 481, "end": 482}, "1": {"file_hash": [122, 204, 7, 1, 205, 54, 3, 51, 125, 214, 94, 62, 42, 78, 68, 201, 176, 15, 130, 139, 9, 152, 202, 13, 253, 110, 176, 140, 177, 223, 120, 245], "start": 484, "end": 493}, "2": {"file_hash": [122, 204, 7, 1, 205, 54, 3, 51, 125, 214, 94, 62, 42, 78, 68, 201, 176, 15, 130, 139, 9, 152, 202, 13, 253, 110, 176, 140, 177, 223, 120, 245], "start": 495, "end": 498}, "3": {"file_hash": [122, 204, 7, 1, 205, 54, 3, 51, 125, 214, 94, 62, 42, 78, 68, 201, 176, 15, 130, 139, 9, 152, 202, 13, 253, 110, 176, 140, 177, 223, 120, 245], "start": 463, "end": 499}, "4": {"file_hash": [122, 204, 7, 1, 205, 54, 3, 51, 125, 214, 94, 62, 42, 78, 68, 201, 176, 15, 130, 139, 9, 152, 202, 13, 253, 110, 176, 140, 177, 223, 120, 245], "start": 429, "end": 506}}, "is_native": false}, "1": {"location": {"file_hash": [122, 204, 7, 1, 205, 54, 3, 51, 125, 214, 94, 62, 42, 78, 68, 201, 176, 15, 130, 139, 9, 152, 202, 13, 253, 110, 176, 140, 177, 223, 120, 245], "start": 643, "end": 802}, "definition_location": {"file_hash": [122, 204, 7, 1, 205, 54, 3, 51, 125, 214, 94, 62, 42, 78, 68, 201, 176, 15, 130, 139, 9, 152, 202, 13, 253, 110, 176, 140, 177, 223, 120, 245], "start": 663, "end": 691}, "type_parameters": [], "parameters": [["self#0#0", {"file_hash": [122, 204, 7, 1, 205, 54, 3, 51, 125, 214, 94, 62, 42, 78, 68, 201, 176, 15, 130, 139, 9, 152, 202, 13, 253, 110, 176, 140, 177, 223, 120, 245], "start": 692, "end": 696}]], "returns": [{"file_hash": [122, 204, 7, 1, 205, 54, 3, 51, 125, 214, 94, 62, 42, 78, 68, 201, 176, 15, 130, 139, 9, 152, 202, 13, 253, 110, 176, 140, 177, 223, 120, 245], "start": 722, "end": 736}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [122, 204, 7, 1, 205, 54, 3, 51, 125, 214, 94, 62, 42, 78, 68, 201, 176, 15, 130, 139, 9, 152, 202, 13, 253, 110, 176, 140, 177, 223, 120, 245], "start": 743, "end": 747}, "2": {"file_hash": [122, 204, 7, 1, 205, 54, 3, 51, 125, 214, 94, 62, 42, 78, 68, 201, 176, 15, 130, 139, 9, 152, 202, 13, 253, 110, 176, 140, 177, 223, 120, 245], "start": 743, "end": 767}, "3": {"file_hash": [122, 204, 7, 1, 205, 54, 3, 51, 125, 214, 94, 62, 42, 78, 68, 201, 176, 15, 130, 139, 9, 152, 202, 13, 253, 110, 176, 140, 177, 223, 120, 245], "start": 773, "end": 777}, "4": {"file_hash": [122, 204, 7, 1, 205, 54, 3, 51, 125, 214, 94, 62, 42, 78, 68, 201, 176, 15, 130, 139, 9, 152, 202, 13, 253, 110, 176, 140, 177, 223, 120, 245], "start": 773, "end": 783}, "5": {"file_hash": [122, 204, 7, 1, 205, 54, 3, 51, 125, 214, 94, 62, 42, 78, 68, 201, 176, 15, 130, 139, 9, 152, 202, 13, 253, 110, 176, 140, 177, 223, 120, 245], "start": 773, "end": 800}}, "is_native": false}, "2": {"location": {"file_hash": [122, 204, 7, 1, 205, 54, 3, 51, 125, 214, 94, 62, 42, 78, 68, 201, 176, 15, 130, 139, 9, 152, 202, 13, 253, 110, 176, 140, 177, 223, 120, 245], "start": 869, "end": 1028}, "definition_location": {"file_hash": [122, 204, 7, 1, 205, 54, 3, 51, 125, 214, 94, 62, 42, 78, 68, 201, 176, 15, 130, 139, 9, 152, 202, 13, 253, 110, 176, 140, 177, 223, 120, 245], "start": 889, "end": 896}, "type_parameters": [], "parameters": [["self#0#0", {"file_hash": [122, 204, 7, 1, 205, 54, 3, 51, 125, 214, 94, 62, 42, 78, 68, 201, 176, 15, 130, 139, 9, 152, 202, 13, 253, 110, 176, 140, 177, 223, 120, 245], "start": 897, "end": 901}]], "returns": [{"file_hash": [122, 204, 7, 1, 205, 54, 3, 51, 125, 214, 94, 62, 42, 78, 68, 201, 176, 15, 130, 139, 9, 152, 202, 13, 253, 110, 176, 140, 177, 223, 120, 245], "start": 922, "end": 931}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [122, 204, 7, 1, 205, 54, 3, 51, 125, 214, 94, 62, 42, 78, 68, 201, 176, 15, 130, 139, 9, 152, 202, 13, 253, 110, 176, 140, 177, 223, 120, 245], "start": 956, "end": 961}, "1": {"file_hash": [122, 204, 7, 1, 205, 54, 3, 51, 125, 214, 94, 62, 42, 78, 68, 201, 176, 15, 130, 139, 9, 152, 202, 13, 253, 110, 176, 140, 177, 223, 120, 245], "start": 938, "end": 962}, "2": {"file_hash": [122, 204, 7, 1, 205, 54, 3, 51, 125, 214, 94, 62, 42, 78, 68, 201, 176, 15, 130, 139, 9, 152, 202, 13, 253, 110, 176, 140, 177, 223, 120, 245], "start": 1001, "end": 1005}, "3": {"file_hash": [122, 204, 7, 1, 205, 54, 3, 51, 125, 214, 94, 62, 42, 78, 68, 201, 176, 15, 130, 139, 9, 152, 202, 13, 253, 110, 176, 140, 177, 223, 120, 245], "start": 972, "end": 998}, "4": {"file_hash": [122, 204, 7, 1, 205, 54, 3, 51, 125, 214, 94, 62, 42, 78, 68, 201, 176, 15, 130, 139, 9, 152, 202, 13, 253, 110, 176, 140, 177, 223, 120, 245], "start": 1011, "end": 1026}}, "is_native": false}, "3": {"location": {"file_hash": [122, 204, 7, 1, 205, 54, 3, 51, 125, 214, 94, 62, 42, 78, 68, 201, 176, 15, 130, 139, 9, 152, 202, 13, 253, 110, 176, 140, 177, 223, 120, 245], "start": 1243, "end": 1449}, "definition_location": {"file_hash": [122, 204, 7, 1, 205, 54, 3, 51, 125, 214, 94, 62, 42, 78, 68, 201, 176, 15, 130, 139, 9, 152, 202, 13, 253, 110, 176, 140, 177, 223, 120, 245], "start": 1247, "end": 1264}, "type_parameters": [], "parameters": [["self#0#0", {"file_hash": [122, 204, 7, 1, 205, 54, 3, 51, 125, 214, 94, 62, 42, 78, 68, 201, 176, 15, 130, 139, 9, 152, 202, 13, 253, 110, 176, 140, 177, 223, 120, 245], "start": 1265, "end": 1269}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [122, 204, 7, 1, 205, 54, 3, 51, 125, 214, 94, 62, 42, 78, 68, 201, 176, 15, 130, 139, 9, 152, 202, 13, 253, 110, 176, 140, 177, 223, 120, 245], "start": 1310, "end": 1314}, "1": {"file_hash": [122, 204, 7, 1, 205, 54, 3, 51, 125, 214, 94, 62, 42, 78, 68, 201, 176, 15, 130, 139, 9, 152, 202, 13, 253, 110, 176, 140, 177, 223, 120, 245], "start": 1310, "end": 1324}, "2": {"file_hash": [122, 204, 7, 1, 205, 54, 3, 51, 125, 214, 94, 62, 42, 78, 68, 201, 176, 15, 130, 139, 9, 152, 202, 13, 253, 110, 176, 140, 177, 223, 120, 245], "start": 1427, "end": 1428}, "3": {"file_hash": [122, 204, 7, 1, 205, 54, 3, 51, 125, 214, 94, 62, 42, 78, 68, 201, 176, 15, 130, 139, 9, 152, 202, 13, 253, 110, 176, 140, 177, 223, 120, 245], "start": 1424, "end": 1426}, "4": {"file_hash": [122, 204, 7, 1, 205, 54, 3, 51, 125, 214, 94, 62, 42, 78, 68, 201, 176, 15, 130, 139, 9, 152, 202, 13, 253, 110, 176, 140, 177, 223, 120, 245], "start": 1408, "end": 1446}, "6": {"file_hash": [122, 204, 7, 1, 205, 54, 3, 51, 125, 214, 94, 62, 42, 78, 68, 201, 176, 15, 130, 139, 9, 152, 202, 13, 253, 110, 176, 140, 177, 223, 120, 245], "start": 1430, "end": 1445}, "7": {"file_hash": [122, 204, 7, 1, 205, 54, 3, 51, 125, 214, 94, 62, 42, 78, 68, 201, 176, 15, 130, 139, 9, 152, 202, 13, 253, 110, 176, 140, 177, 223, 120, 245], "start": 1408, "end": 1446}, "8": {"file_hash": [122, 204, 7, 1, 205, 54, 3, 51, 125, 214, 94, 62, 42, 78, 68, 201, 176, 15, 130, 139, 9, 152, 202, 13, 253, 110, 176, 140, 177, 223, 120, 245], "start": 1446, "end": 1447}}, "is_native": false}, "4": {"location": {"file_hash": [122, 204, 7, 1, 205, 54, 3, 51, 125, 214, 94, 62, 42, 78, 68, 201, 176, 15, 130, 139, 9, 152, 202, 13, 253, 110, 176, 140, 177, 223, 120, 245], "start": 1451, "end": 1521}, "definition_location": {"file_hash": [122, 204, 7, 1, 205, 54, 3, 51, 125, 214, 94, 62, 42, 78, 68, 201, 176, 15, 130, 139, 9, 152, 202, 13, 253, 110, 176, 140, 177, 223, 120, 245], "start": 1455, "end": 1462}, "type_parameters": [], "parameters": [["self#0#0", {"file_hash": [122, 204, 7, 1, 205, 54, 3, 51, 125, 214, 94, 62, 42, 78, 68, 201, 176, 15, 130, 139, 9, 152, 202, 13, 253, 110, 176, 140, 177, 223, 120, 245], "start": 1463, "end": 1467}]], "returns": [{"file_hash": [122, 204, 7, 1, 205, 54, 3, 51, 125, 214, 94, 62, 42, 78, 68, 201, 176, 15, 130, 139, 9, 152, 202, 13, 253, 110, 176, 140, 177, 223, 120, 245], "start": 1489, "end": 1492}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [122, 204, 7, 1, 205, 54, 3, 51, 125, 214, 94, 62, 42, 78, 68, 201, 176, 15, 130, 139, 9, 152, 202, 13, 253, 110, 176, 140, 177, 223, 120, 245], "start": 1499, "end": 1503}, "1": {"file_hash": [122, 204, 7, 1, 205, 54, 3, 51, 125, 214, 94, 62, 42, 78, 68, 201, 176, 15, 130, 139, 9, 152, 202, 13, 253, 110, 176, 140, 177, 223, 120, 245], "start": 1499, "end": 1509}, "2": {"file_hash": [122, 204, 7, 1, 205, 54, 3, 51, 125, 214, 94, 62, 42, 78, 68, 201, 176, 15, 130, 139, 9, 152, 202, 13, 253, 110, 176, 140, 177, 223, 120, 245], "start": 1499, "end": 1519}}, "is_native": false}}, "constant_map": {"EInvalidVersion": 0}}