{"version": 2, "from_file_path": "/Users/<USER>/.move/https___github_com_MystenLabs_sui_git_b448b1d971bd6c1aac8ef4eee4305943806d5d5b/crates/sui-framework/packages/sui-system/sources/validator_cap.move", "definition_location": {"file_hash": [185, 34, 18, 57, 177, 181, 192, 195, 35, 137, 122, 87, 10, 92, 41, 199, 108, 200, 172, 73, 112, 139, 77, 29, 131, 140, 210, 65, 69, 125, 142, 179], "start": 94, "end": 107}, "module_name": ["0000000000000000000000000000000000000000000000000000000000000003", "validator_cap"], "struct_map": {"0": {"definition_location": {"file_hash": [185, 34, 18, 57, 177, 181, 192, 195, 35, 137, 122, 87, 10, 92, 41, 199, 108, 200, 172, 73, 112, 139, 77, 29, 131, 140, 210, 65, 69, 125, 142, 179], "start": 913, "end": 944}, "type_parameters": [], "fields": [{"file_hash": [185, 34, 18, 57, 177, 181, 192, 195, 35, 137, 122, 87, 10, 92, 41, 199, 108, 200, 172, 73, 112, 139, 77, 29, 131, 140, 210, 65, 69, 125, 142, 179], "start": 966, "end": 968}, {"file_hash": [185, 34, 18, 57, 177, 181, 192, 195, 35, 137, 122, 87, 10, 92, 41, 199, 108, 200, 172, 73, 112, 139, 77, 29, 131, 140, 210, 65, 69, 125, 142, 179], "start": 979, "end": 1007}]}, "1": {"definition_location": {"file_hash": [185, 34, 18, 57, 177, 181, 192, 195, 35, 137, 122, 87, 10, 92, 41, 199, 108, 200, 172, 73, 112, 139, 77, 29, 131, 140, 210, 65, 69, 125, 142, 179], "start": 1175, "end": 1196}, "type_parameters": [], "fields": [{"file_hash": [185, 34, 18, 57, 177, 181, 192, 195, 35, 137, 122, 87, 10, 92, 41, 199, 108, 200, 172, 73, 112, 139, 77, 29, 131, 140, 210, 65, 69, 125, 142, 179], "start": 1212, "end": 1240}]}}, "enum_map": {}, "function_map": {"0": {"location": {"file_hash": [185, 34, 18, 57, 177, 181, 192, 195, 35, 137, 122, 87, 10, 92, 41, 199, 108, 200, 172, 73, 112, 139, 77, 29, 131, 140, 210, 65, 69, 125, 142, 179], "start": 1254, "end": 1404}, "definition_location": {"file_hash": [185, 34, 18, 57, 177, 181, 192, 195, 35, 137, 122, 87, 10, 92, 41, 199, 108, 200, 172, 73, 112, 139, 77, 29, 131, 140, 210, 65, 69, 125, 142, 179], "start": 1274, "end": 1306}, "type_parameters": [], "parameters": [["cap#0#0", {"file_hash": [185, 34, 18, 57, 177, 181, 192, 195, 35, 137, 122, 87, 10, 92, 41, 199, 108, 200, 172, 73, 112, 139, 77, 29, 131, 140, 210, 65, 69, 125, 142, 179], "start": 1312, "end": 1315}]], "returns": [{"file_hash": [185, 34, 18, 57, 177, 181, 192, 195, 35, 137, 122, 87, 10, 92, 41, 199, 108, 200, 172, 73, 112, 139, 77, 29, 131, 140, 210, 65, 69, 125, 142, 179], "start": 1354, "end": 1362}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [185, 34, 18, 57, 177, 181, 192, 195, 35, 137, 122, 87, 10, 92, 41, 199, 108, 200, 172, 73, 112, 139, 77, 29, 131, 140, 210, 65, 69, 125, 142, 179], "start": 1370, "end": 1373}, "1": {"file_hash": [185, 34, 18, 57, 177, 181, 192, 195, 35, 137, 122, 87, 10, 92, 41, 199, 108, 200, 172, 73, 112, 139, 77, 29, 131, 140, 210, 65, 69, 125, 142, 179], "start": 1369, "end": 1402}}, "is_native": false}, "1": {"location": {"file_hash": [185, 34, 18, 57, 177, 181, 192, 195, 35, 137, 122, 87, 10, 92, 41, 199, 108, 200, 172, 73, 112, 139, 77, 29, 131, 140, 210, 65, 69, 125, 142, 179], "start": 1406, "end": 1537}, "definition_location": {"file_hash": [185, 34, 18, 57, 177, 181, 192, 195, 35, 137, 122, 87, 10, 92, 41, 199, 108, 200, 172, 73, 112, 139, 77, 29, 131, 140, 210, 65, 69, 125, 142, 179], "start": 1426, "end": 1456}, "type_parameters": [], "parameters": [["cap#0#0", {"file_hash": [185, 34, 18, 57, 177, 181, 192, 195, 35, 137, 122, 87, 10, 92, 41, 199, 108, 200, 172, 73, 112, 139, 77, 29, 131, 140, 210, 65, 69, 125, 142, 179], "start": 1457, "end": 1460}]], "returns": [{"file_hash": [185, 34, 18, 57, 177, 181, 192, 195, 35, 137, 122, 87, 10, 92, 41, 199, 108, 200, 172, 73, 112, 139, 77, 29, 131, 140, 210, 65, 69, 125, 142, 179], "start": 1487, "end": 1495}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [185, 34, 18, 57, 177, 181, 192, 195, 35, 137, 122, 87, 10, 92, 41, 199, 108, 200, 172, 73, 112, 139, 77, 29, 131, 140, 210, 65, 69, 125, 142, 179], "start": 1503, "end": 1506}, "1": {"file_hash": [185, 34, 18, 57, 177, 181, 192, 195, 35, 137, 122, 87, 10, 92, 41, 199, 108, 200, 172, 73, 112, 139, 77, 29, 131, 140, 210, 65, 69, 125, 142, 179], "start": 1502, "end": 1535}}, "is_native": false}, "2": {"location": {"file_hash": [185, 34, 18, 57, 177, 181, 192, 195, 35, 137, 122, 87, 10, 92, 41, 199, 108, 200, 172, 73, 112, 139, 77, 29, 131, 140, 210, 65, 69, 125, 142, 179], "start": 1673, "end": 2429}, "definition_location": {"file_hash": [185, 34, 18, 57, 177, 181, 192, 195, 35, 137, 122, 87, 10, 92, 41, 199, 108, 200, 172, 73, 112, 139, 77, 29, 131, 140, 210, 65, 69, 125, 142, 179], "start": 1693, "end": 1744}, "type_parameters": [], "parameters": [["validator_address#0#0", {"file_hash": [185, 34, 18, 57, 177, 181, 192, 195, 35, 137, 122, 87, 10, 92, 41, 199, 108, 200, 172, 73, 112, 139, 77, 29, 131, 140, 210, 65, 69, 125, 142, 179], "start": 1750, "end": 1767}], ["ctx#0#0", {"file_hash": [185, 34, 18, 57, 177, 181, 192, 195, 35, 137, 122, 87, 10, 92, 41, 199, 108, 200, 172, 73, 112, 139, 77, 29, 131, 140, 210, 65, 69, 125, 142, 179], "start": 1782, "end": 1785}]], "returns": [{"file_hash": [185, 34, 18, 57, 177, 181, 192, 195, 35, 137, 122, 87, 10, 92, 41, 199, 108, 200, 172, 73, 112, 139, 77, 29, 131, 140, 210, 65, 69, 125, 142, 179], "start": 1806, "end": 1808}], "locals": [["%#1", {"file_hash": [185, 34, 18, 57, 177, 181, 192, 195, 35, 137, 122, 87, 10, 92, 41, 199, 108, 200, 172, 73, 112, 139, 77, 29, 131, 140, 210, 65, 69, 125, 142, 179], "start": 2067, "end": 2128}], ["operation_cap#1#0", {"file_hash": [185, 34, 18, 57, 177, 181, 192, 195, 35, 137, 122, 87, 10, 92, 41, 199, 108, 200, 172, 73, 112, 139, 77, 29, 131, 140, 210, 65, 69, 125, 142, 179], "start": 2143, "end": 2156}], ["operation_cap_id#1#0", {"file_hash": [185, 34, 18, 57, 177, 181, 192, 195, 35, 137, 122, 87, 10, 92, 41, 199, 108, 200, 172, 73, 112, 139, 77, 29, 131, 140, 210, 65, 69, 125, 142, 179], "start": 2295, "end": 2311}], ["sender_address#1#0", {"file_hash": [185, 34, 18, 57, 177, 181, 192, 195, 35, 137, 122, 87, 10, 92, 41, 199, 108, 200, 172, 73, 112, 139, 77, 29, 131, 140, 210, 65, 69, 125, 142, 179], "start": 2024, "end": 2038}]], "nops": {}, "code_map": {"0": {"file_hash": [185, 34, 18, 57, 177, 181, 192, 195, 35, 137, 122, 87, 10, 92, 41, 199, 108, 200, 172, 73, 112, 139, 77, 29, 131, 140, 210, 65, 69, 125, 142, 179], "start": 2041, "end": 2044}, "2": {"file_hash": [185, 34, 18, 57, 177, 181, 192, 195, 35, 137, 122, 87, 10, 92, 41, 199, 108, 200, 172, 73, 112, 139, 77, 29, 131, 140, 210, 65, 69, 125, 142, 179], "start": 2041, "end": 2053}, "3": {"file_hash": [185, 34, 18, 57, 177, 181, 192, 195, 35, 137, 122, 87, 10, 92, 41, 199, 108, 200, 172, 73, 112, 139, 77, 29, 131, 140, 210, 65, 69, 125, 142, 179], "start": 2024, "end": 2038}, "4": {"file_hash": [185, 34, 18, 57, 177, 181, 192, 195, 35, 137, 122, 87, 10, 92, 41, 199, 108, 200, 172, 73, 112, 139, 77, 29, 131, 140, 210, 65, 69, 125, 142, 179], "start": 2067, "end": 2081}, "5": {"file_hash": [185, 34, 18, 57, 177, 181, 192, 195, 35, 137, 122, 87, 10, 92, 41, 199, 108, 200, 172, 73, 112, 139, 77, 29, 131, 140, 210, 65, 69, 125, 142, 179], "start": 2085, "end": 2089}, "6": {"file_hash": [185, 34, 18, 57, 177, 181, 192, 195, 35, 137, 122, 87, 10, 92, 41, 199, 108, 200, 172, 73, 112, 139, 77, 29, 131, 140, 210, 65, 69, 125, 142, 179], "start": 2082, "end": 2084}, "7": {"file_hash": [185, 34, 18, 57, 177, 181, 192, 195, 35, 137, 122, 87, 10, 92, 41, 199, 108, 200, 172, 73, 112, 139, 77, 29, 131, 140, 210, 65, 69, 125, 142, 179], "start": 2067, "end": 2128}, "11": {"file_hash": [185, 34, 18, 57, 177, 181, 192, 195, 35, 137, 122, 87, 10, 92, 41, 199, 108, 200, 172, 73, 112, 139, 77, 29, 131, 140, 210, 65, 69, 125, 142, 179], "start": 2093, "end": 2107}, "12": {"file_hash": [185, 34, 18, 57, 177, 181, 192, 195, 35, 137, 122, 87, 10, 92, 41, 199, 108, 200, 172, 73, 112, 139, 77, 29, 131, 140, 210, 65, 69, 125, 142, 179], "start": 2111, "end": 2128}, "13": {"file_hash": [185, 34, 18, 57, 177, 181, 192, 195, 35, 137, 122, 87, 10, 92, 41, 199, 108, 200, 172, 73, 112, 139, 77, 29, 131, 140, 210, 65, 69, 125, 142, 179], "start": 2108, "end": 2110}, "14": {"file_hash": [185, 34, 18, 57, 177, 181, 192, 195, 35, 137, 122, 87, 10, 92, 41, 199, 108, 200, 172, 73, 112, 139, 77, 29, 131, 140, 210, 65, 69, 125, 142, 179], "start": 2067, "end": 2128}, "16": {"file_hash": [185, 34, 18, 57, 177, 181, 192, 195, 35, 137, 122, 87, 10, 92, 41, 199, 108, 200, 172, 73, 112, 139, 77, 29, 131, 140, 210, 65, 69, 125, 142, 179], "start": 2059, "end": 2132}, "20": {"file_hash": [185, 34, 18, 57, 177, 181, 192, 195, 35, 137, 122, 87, 10, 92, 41, 199, 108, 200, 172, 73, 112, 139, 77, 29, 131, 140, 210, 65, 69, 125, 142, 179], "start": 2130, "end": 2131}, "21": {"file_hash": [185, 34, 18, 57, 177, 181, 192, 195, 35, 137, 122, 87, 10, 92, 41, 199, 108, 200, 172, 73, 112, 139, 77, 29, 131, 140, 210, 65, 69, 125, 142, 179], "start": 2059, "end": 2132}, "22": {"file_hash": [185, 34, 18, 57, 177, 181, 192, 195, 35, 137, 122, 87, 10, 92, 41, 199, 108, 200, 172, 73, 112, 139, 77, 29, 131, 140, 210, 65, 69, 125, 142, 179], "start": 2217, "end": 2220}, "23": {"file_hash": [185, 34, 18, 57, 177, 181, 192, 195, 35, 137, 122, 87, 10, 92, 41, 199, 108, 200, 172, 73, 112, 139, 77, 29, 131, 140, 210, 65, 69, 125, 142, 179], "start": 2205, "end": 2221}, "24": {"file_hash": [185, 34, 18, 57, 177, 181, 192, 195, 35, 137, 122, 87, 10, 92, 41, 199, 108, 200, 172, 73, 112, 139, 77, 29, 131, 140, 210, 65, 69, 125, 142, 179], "start": 2261, "end": 2278}, "25": {"file_hash": [185, 34, 18, 57, 177, 181, 192, 195, 35, 137, 122, 87, 10, 92, 41, 199, 108, 200, 172, 73, 112, 139, 77, 29, 131, 140, 210, 65, 69, 125, 142, 179], "start": 2159, "end": 2285}, "26": {"file_hash": [185, 34, 18, 57, 177, 181, 192, 195, 35, 137, 122, 87, 10, 92, 41, 199, 108, 200, 172, 73, 112, 139, 77, 29, 131, 140, 210, 65, 69, 125, 142, 179], "start": 2143, "end": 2156}, "27": {"file_hash": [185, 34, 18, 57, 177, 181, 192, 195, 35, 137, 122, 87, 10, 92, 41, 199, 108, 200, 172, 73, 112, 139, 77, 29, 131, 140, 210, 65, 69, 125, 142, 179], "start": 2325, "end": 2339}, "28": {"file_hash": [185, 34, 18, 57, 177, 181, 192, 195, 35, 137, 122, 87, 10, 92, 41, 199, 108, 200, 172, 73, 112, 139, 77, 29, 131, 140, 210, 65, 69, 125, 142, 179], "start": 2314, "end": 2340}, "29": {"file_hash": [185, 34, 18, 57, 177, 181, 192, 195, 35, 137, 122, 87, 10, 92, 41, 199, 108, 200, 172, 73, 112, 139, 77, 29, 131, 140, 210, 65, 69, 125, 142, 179], "start": 2295, "end": 2311}, "30": {"file_hash": [185, 34, 18, 57, 177, 181, 192, 195, 35, 137, 122, 87, 10, 92, 41, 199, 108, 200, 172, 73, 112, 139, 77, 29, 131, 140, 210, 65, 69, 125, 142, 179], "start": 2372, "end": 2385}, "31": {"file_hash": [185, 34, 18, 57, 177, 181, 192, 195, 35, 137, 122, 87, 10, 92, 41, 199, 108, 200, 172, 73, 112, 139, 77, 29, 131, 140, 210, 65, 69, 125, 142, 179], "start": 2387, "end": 2404}, "32": {"file_hash": [185, 34, 18, 57, 177, 181, 192, 195, 35, 137, 122, 87, 10, 92, 41, 199, 108, 200, 172, 73, 112, 139, 77, 29, 131, 140, 210, 65, 69, 125, 142, 179], "start": 2346, "end": 2405}, "33": {"file_hash": [185, 34, 18, 57, 177, 181, 192, 195, 35, 137, 122, 87, 10, 92, 41, 199, 108, 200, 172, 73, 112, 139, 77, 29, 131, 140, 210, 65, 69, 125, 142, 179], "start": 2411, "end": 2427}}, "is_native": false}, "3": {"location": {"file_hash": [185, 34, 18, 57, 177, 181, 192, 195, 35, 137, 122, 87, 10, 92, 41, 199, 108, 200, 172, 73, 112, 139, 77, 29, 131, 140, 210, 65, 69, 125, 142, 179], "start": 2580, "end": 2772}, "definition_location": {"file_hash": [185, 34, 18, 57, 177, 181, 192, 195, 35, 137, 122, 87, 10, 92, 41, 199, 108, 200, 172, 73, 112, 139, 77, 29, 131, 140, 210, 65, 69, 125, 142, 179], "start": 2600, "end": 2613}, "type_parameters": [], "parameters": [["cap#0#0", {"file_hash": [185, 34, 18, 57, 177, 181, 192, 195, 35, 137, 122, 87, 10, 92, 41, 199, 108, 200, 172, 73, 112, 139, 77, 29, 131, 140, 210, 65, 69, 125, 142, 179], "start": 2614, "end": 2617}]], "returns": [{"file_hash": [185, 34, 18, 57, 177, 181, 192, 195, 35, 137, 122, 87, 10, 92, 41, 199, 108, 200, 172, 73, 112, 139, 77, 29, 131, 140, 210, 65, 69, 125, 142, 179], "start": 2654, "end": 2675}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [185, 34, 18, 57, 177, 181, 192, 195, 35, 137, 122, 87, 10, 92, 41, 199, 108, 200, 172, 73, 112, 139, 77, 29, 131, 140, 210, 65, 69, 125, 142, 179], "start": 2736, "end": 2739}, "1": {"file_hash": [185, 34, 18, 57, 177, 181, 192, 195, 35, 137, 122, 87, 10, 92, 41, 199, 108, 200, 172, 73, 112, 139, 77, 29, 131, 140, 210, 65, 69, 125, 142, 179], "start": 2736, "end": 2768}, "3": {"file_hash": [185, 34, 18, 57, 177, 181, 192, 195, 35, 137, 122, 87, 10, 92, 41, 199, 108, 200, 172, 73, 112, 139, 77, 29, 131, 140, 210, 65, 69, 125, 142, 179], "start": 2682, "end": 2770}}, "is_native": false}}, "constant_map": {}}