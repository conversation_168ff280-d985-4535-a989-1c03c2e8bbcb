{"version": 2, "from_file_path": "/Users/<USER>/.move/https___github_com_MystenLabs_sui_git_b448b1d971bd6c1aac8ef4eee4305943806d5d5b/crates/sui-framework/packages/sui-framework/sources/borrow.move", "definition_location": {"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 383, "end": 389}, "module_name": ["0000000000000000000000000000000000000000000000000000000000000002", "borrow"], "struct_map": {"0": {"definition_location": {"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 653, "end": 661}, "type_parameters": [["T", {"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 662, "end": 663}]], "fields": [{"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 694, "end": 696}, {"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 711, "end": 716}]}, "1": {"definition_location": {"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 813, "end": 819}, "type_parameters": [], "fields": [{"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 822, "end": 825}, {"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 836, "end": 839}]}}, "enum_map": {}, "function_map": {"0": {"location": {"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 882, "end": 1057}, "definition_location": {"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 893, "end": 896}, "type_parameters": [["T", {"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 897, "end": 898}]], "parameters": [["value#0#0", {"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 913, "end": 918}], ["ctx#0#0", {"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 923, "end": 926}]], "returns": [{"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 945, "end": 956}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 986, "end": 989}, "1": {"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 986, "end": 1012}, "2": {"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 1042, "end": 1047}, "3": {"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 1029, "end": 1048}, "4": {"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 963, "end": 1055}}, "is_native": false}, "1": {"location": {"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 1148, "end": 1396}, "definition_location": {"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 1159, "end": 1165}, "type_parameters": [["T", {"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 1166, "end": 1167}]], "parameters": [["self#0#0", {"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 1182, "end": 1186}]], "returns": [{"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 1208, "end": 1209}, {"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 1211, "end": 1217}], "locals": [["id#1#0", {"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 1267, "end": 1269}], ["value#1#0", {"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 1229, "end": 1234}]], "nops": {}, "code_map": {"0": {"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 1237, "end": 1241}, "1": {"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 1237, "end": 1247}, "2": {"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 1237, "end": 1257}, "3": {"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 1229, "end": 1234}, "4": {"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 1283, "end": 1289}, "5": {"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 1272, "end": 1290}, "6": {"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 1267, "end": 1269}, "7": {"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 1307, "end": 1312}, "8": {"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 1348, "end": 1352}, "9": {"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 1348, "end": 1355}, "11": {"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 1374, "end": 1376}, "12": {"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 1322, "end": 1387}, "13": {"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 1297, "end": 1394}}, "is_native": false}, "2": {"location": {"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 1450, "end": 1702}, "definition_location": {"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 1461, "end": 1469}, "type_parameters": [["T", {"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 1470, "end": 1471}]], "parameters": [["self#0#0", {"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 1486, "end": 1490}], ["value#0#0", {"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 1510, "end": 1515}], ["borrow#0#0", {"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 1520, "end": 1526}]], "returns": [], "locals": [["obj#1#0", {"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 1560, "end": 1563}], ["ref#1#0", {"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 1555, "end": 1558}]], "nops": {}, "code_map": {"0": {"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 1568, "end": 1574}, "1": {"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 1546, "end": 1565}, "2": {"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 1560, "end": 1563}, "3": {"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 1555, "end": 1558}, "4": {"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 1600, "end": 1606}, "5": {"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 1589, "end": 1607}, "6": {"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 1611, "end": 1614}, "7": {"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 1608, "end": 1610}, "8": {"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 1581, "end": 1628}, "12": {"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 1616, "end": 1627}, "13": {"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 1581, "end": 1628}, "14": {"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 1642, "end": 1646}, "15": {"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 1642, "end": 1649}, "17": {"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 1653, "end": 1656}, "18": {"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 1650, "end": 1652}, "19": {"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 1634, "end": 1671}, "23": {"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 1658, "end": 1670}, "24": {"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 1634, "end": 1671}, "25": {"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 1677, "end": 1681}, "26": {"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 1677, "end": 1687}, "27": {"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 1693, "end": 1698}, "28": {"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 1677, "end": 1699}, "29": {"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 1699, "end": 1700}}, "is_native": false}, "3": {"location": {"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 1759, "end": 1886}, "definition_location": {"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 1770, "end": 1777}, "type_parameters": [["T", {"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 1778, "end": 1779}]], "parameters": [["self#0#0", {"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 1794, "end": 1798}]], "returns": [{"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 1814, "end": 1815}], "locals": [["value#1#0", {"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 1844, "end": 1849}]], "nops": {}, "code_map": {"0": {"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 1854, "end": 1858}, "1": {"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 1826, "end": 1851}, "2": {"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 1844, "end": 1849}, "3": {"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 1841, "end": 1842}, "4": {"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 1864, "end": 1869}, "5": {"file_hash": [50, 187, 103, 15, 25, 110, 233, 112, 136, 194, 233, 231, 239, 193, 17, 22, 92, 151, 172, 198, 50, 101, 167, 148, 26, 129, 35, 59, 198, 61, 149, 101], "start": 1864, "end": 1884}}, "is_native": false}}, "constant_map": {"EWrongBorrow": 0, "EWrongValue": 1}}