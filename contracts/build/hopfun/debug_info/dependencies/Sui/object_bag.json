{"version": 2, "from_file_path": "/Users/<USER>/.move/https___github_com_MystenLabs_sui_git_b448b1d971bd6c1aac8ef4eee4305943806d5d5b/crates/sui-framework/packages/sui-framework/sources/object_bag.move", "definition_location": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 434, "end": 444}, "module_name": ["0000000000000000000000000000000000000000000000000000000000000002", "object_bag"], "struct_map": {"0": {"definition_location": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 573, "end": 582}, "type_parameters": [], "fields": [{"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 631, "end": 633}, {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 693, "end": 697}]}}, "enum_map": {}, "function_map": {"0": {"location": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 736, "end": 855}, "definition_location": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 747, "end": 750}, "type_parameters": [], "parameters": [["ctx#0#0", {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 751, "end": 754}]], "returns": [{"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 773, "end": 782}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 825, "end": 828}, "1": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 813, "end": 829}, "2": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 845, "end": 846}, "3": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 789, "end": 853}}, "is_native": false}, "1": {"location": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 1032, "end": 1188}, "definition_location": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 1043, "end": 1046}, "type_parameters": [["K", {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 1047, "end": 1048}], ["V", {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 1071, "end": 1072}]], "parameters": [["bag#0#0", {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 1087, "end": 1090}], ["k#0#0", {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 1108, "end": 1109}], ["v#0#0", {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 1114, "end": 1115}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 1143, "end": 1146}, "1": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 1138, "end": 1149}, "2": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 1151, "end": 1152}, "3": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 1154, "end": 1155}, "4": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 1126, "end": 1156}, "5": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 1173, "end": 1176}, "6": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 1173, "end": 1181}, "8": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 1184, "end": 1185}, "9": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 1182, "end": 1183}, "10": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 1162, "end": 1165}, "11": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 1162, "end": 1170}, "12": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 1162, "end": 1185}, "13": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 1185, "end": 1186}}, "is_native": false}, "2": {"location": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 1556, "end": 1675}, "definition_location": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 1567, "end": 1573}, "type_parameters": [["K", {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 1574, "end": 1575}], ["V", {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 1598, "end": 1599}]], "parameters": [["bag#0#0", {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 1614, "end": 1617}], ["k#0#0", {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 1631, "end": 1632}]], "returns": [{"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 1638, "end": 1640}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 1663, "end": 1666}, "1": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 1662, "end": 1669}, "2": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 1671, "end": 1672}, "3": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 1647, "end": 1673}}, "is_native": false}, "3": {"location": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 2045, "end": 2184}, "definition_location": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 2056, "end": 2066}, "type_parameters": [["K", {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 2067, "end": 2068}], ["V", {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 2091, "end": 2092}]], "parameters": [["bag#0#0", {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 2107, "end": 2110}], ["k#0#0", {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 2128, "end": 2129}]], "returns": [{"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 2135, "end": 2141}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 2172, "end": 2175}, "1": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 2167, "end": 2178}, "2": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 2180, "end": 2181}, "3": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 2148, "end": 2182}}, "is_native": false}, "4": {"location": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 2544, "end": 2714}, "definition_location": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 2555, "end": 2561}, "type_parameters": [["K", {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 2562, "end": 2563}], ["V", {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 2586, "end": 2587}]], "parameters": [["bag#0#0", {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 2602, "end": 2605}], ["k#0#0", {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 2623, "end": 2624}]], "returns": [{"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 2630, "end": 2631}], "locals": [["v#1#0", {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 2642, "end": 2643}]], "nops": {}, "code_map": {"0": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 2666, "end": 2669}, "1": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 2661, "end": 2672}, "2": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 2674, "end": 2675}, "3": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 2646, "end": 2676}, "4": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 2642, "end": 2643}, "5": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 2693, "end": 2696}, "6": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 2693, "end": 2701}, "8": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 2704, "end": 2705}, "9": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 2702, "end": 2703}, "10": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 2682, "end": 2685}, "11": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 2682, "end": 2690}, "12": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 2682, "end": 2705}, "13": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 2711, "end": 2712}}, "is_native": false}, "5": {"location": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 2815, "end": 2926}, "definition_location": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 2826, "end": 2834}, "type_parameters": [["K", {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 2835, "end": 2836}]], "parameters": [["bag#0#0", {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 2859, "end": 2862}], ["k#0#0", {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 2876, "end": 2877}]], "returns": [{"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 2883, "end": 2887}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 2914, "end": 2917}, "1": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 2913, "end": 2920}, "2": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 2922, "end": 2923}, "3": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 2894, "end": 2924}}, "is_native": false}, "6": {"location": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 3066, "end": 3215}, "definition_location": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 3077, "end": 3095}, "type_parameters": [["K", {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 3096, "end": 3097}], ["V", {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 3120, "end": 3121}]], "parameters": [["bag#0#0", {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 3136, "end": 3139}], ["k#0#0", {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 3153, "end": 3154}]], "returns": [{"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 3160, "end": 3164}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 3203, "end": 3206}, "1": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 3202, "end": 3209}, "2": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 3211, "end": 3212}, "3": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 3171, "end": 3213}}, "is_native": false}, "7": {"location": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 3280, "end": 3336}, "definition_location": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 3291, "end": 3297}, "type_parameters": [], "parameters": [["bag#0#0", {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 3298, "end": 3301}]], "returns": [{"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 3316, "end": 3319}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 3326, "end": 3329}, "1": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 3326, "end": 3334}}, "is_native": false}, "8": {"location": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 3402, "end": 3466}, "definition_location": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 3413, "end": 3421}, "type_parameters": [], "parameters": [["bag#0#0", {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 3422, "end": 3425}]], "returns": [{"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 3440, "end": 3444}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 3451, "end": 3454}, "1": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 3451, "end": 3459}, "3": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 3463, "end": 3464}, "4": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 3460, "end": 3462}, "5": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 3451, "end": 3464}}, "is_native": false}, "9": {"location": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 3558, "end": 3694}, "definition_location": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 3569, "end": 3582}, "type_parameters": [], "parameters": [["bag#0#0", {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 3583, "end": 3586}]], "returns": [], "locals": [["id#1#0", {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 3621, "end": 3623}], ["size#1#0", {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 3625, "end": 3629}]], "nops": {}, "code_map": {"0": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 3634, "end": 3637}, "1": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 3609, "end": 3631}, "2": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 3625, "end": 3629}, "3": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 3621, "end": 3623}, "4": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 3651, "end": 3655}, "5": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 3659, "end": 3660}, "6": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 3656, "end": 3658}, "7": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 3643, "end": 3675}, "9": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 3662, "end": 3674}, "10": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 3643, "end": 3675}, "11": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 3681, "end": 3683}, "12": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 3681, "end": 3692}}, "is_native": false}, "10": {"location": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 3820, "end": 3929}, "definition_location": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 3831, "end": 3839}, "type_parameters": [["K", {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 3840, "end": 3841}]], "parameters": [["bag#0#0", {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 3864, "end": 3867}], ["k#0#0", {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 3881, "end": 3882}]], "returns": [{"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 3888, "end": 3898}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 3917, "end": 3920}, "1": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 3916, "end": 3923}, "2": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 3925, "end": 3926}, "3": {"file_hash": [51, 213, 228, 135, 203, 103, 171, 120, 226, 86, 150, 170, 36, 101, 221, 225, 154, 215, 36, 19, 110, 213, 234, 69, 67, 190, 150, 89, 169, 251, 169, 204], "start": 3905, "end": 3927}}, "is_native": false}}, "constant_map": {"EBagNotEmpty": 0}}