#[allow(lint(custom_state_change))]
module hopfun::bonding_curve_v2 {
    use sui::object::{Self, UID, ID};
    use sui::tx_context::{Self, TxContext};
    use sui::transfer;
    use sui::coin::{Self, Coin};
    use sui::balance::{Self, Balance};
    use sui::sui::SUI;
    use sui::clock::{Self, Clock};
    use sui::table::{Self, Table};
    use std::option::{Self, Option};
    
    // use hopfun::errors; // Commented out as errors module is not fully implemented
    use hopfun::events;
    
    // ======== Errors ========
    const ECurveNotFound: u64 = 2001;
    const ECurvePaused: u64 = 2002;
    const ECurveCompleted: u64 = 2003;
    const EInsufficientLiquidity: u64 = 2004;
    const ESlippageExceeded: u64 = 2005;
    const EInvalidAmount: u64 = 2006;
    
    // ======== Constants ========
    const CURVE_COMPLETION_TARGET_BPS: u64 = 8000; // 80% of supply triggers completion
    const MIN_LIQUIDITY: u64 = 1_000_000; // 0.001 SUI minimum
    const BASE_PRICE: u64 = 1_000; // Base price in micro-SUI per token
    const CURVE_STEEPNESS: u64 = 100; // Controls price increase rate
    
    // ======== Structs ========
    
    /// Main bonding curve storage
    struct BondingCurveStorage has key {
        id: UID,
        curves: Table<ID, CurveData>,
        total_curves: u64,
        total_volume: u128,
    }
    
    /// Individual curve data
    struct CurveData has store {
        token_id: ID,
        creator: address,
        
        // Curve parameters
        base_price: u64,
        curve_factor: u64,
        
        // Supply tracking
        current_supply: u64,
        total_supply: u64,
        target_supply: u64,
        circulating_supply: u64,
        
        // Liquidity pool
        token_reserve: u64,
        sui_reserve: Balance<SUI>,
        
        // Trading stats
        total_volume: u128,
        buy_count: u64,
        sell_count: u64,
        unique_traders: u64,
        last_price: u64,
        
        // Status
        is_completed: bool,
        is_paused: bool,
        created_at: u64,
        completed_at: u64,
        
        // DEX migration info
        dex_pool_id: Option<ID>,
    }
    
    /// User position in a curve
    struct UserPosition has store {
        token_balance: u64,
        total_invested: u64,
        realized_profit: u64,
        entry_price: u64,
        last_action_time: u64,
    }
    
    // ======== Initialization ========
    
    fun init(ctx: &mut TxContext) {
        let storage = BondingCurveStorage {
            id: object::new(ctx),
            curves: table::new(ctx),
            total_curves: 0,
            total_volume: 0,
        };
        
        transfer::share_object(storage);
    }
    
    // ======== Public Functions ========
    
    /// Create a new bonding curve for a token
    public fun create_curve(
        storage: &mut BondingCurveStorage,
        token_id: ID,
        total_supply: u64,
        decimals: u8,
        clock: &Clock,
        ctx: &mut TxContext
    ): ID {
        let curve_uid = object::new(ctx);
        let curve_id = object::uid_to_inner(&curve_uid);
        object::delete(curve_uid);
        
        let target_supply = (total_supply * CURVE_COMPLETION_TARGET_BPS) / 10000;
        
        let curve_data = CurveData {
            token_id,
            creator: tx_context::sender(ctx),
            base_price: BASE_PRICE,
            curve_factor: CURVE_STEEPNESS,
            current_supply: 0,
            total_supply,
            target_supply,
            circulating_supply: 0,
            token_reserve: total_supply,
            sui_reserve: balance::zero<SUI>(),
            total_volume: 0,
            buy_count: 0,
            sell_count: 0,
            unique_traders: 0,
            last_price: BASE_PRICE,
            is_completed: false,
            is_paused: false,
            created_at: clock::timestamp_ms(clock),
            completed_at: 0,
            dex_pool_id: option::none(),
        };
        
        table::add(&mut storage.curves, curve_id, curve_data);
        storage.total_curves = storage.total_curves + 1;
        
        // Emit curve creation event
        events::emit_curve_created(
            object::id_to_address(&curve_id),
            object::id_to_address(&token_id),
            tx_context::sender(ctx),
            total_supply,
            BASE_PRICE,
            clock::timestamp_ms(clock)
        );
        
        curve_id
    }
    
    /// Buy tokens from the bonding curve - ENTRY FUNCTION
    public entry fun buy_tokens_entry(
        storage: &mut BondingCurveStorage,
        curve_id: ID,
        sui_payment: Coin<SUI>,
        min_tokens_out: u64,
        clock: &Clock,
        ctx: &mut TxContext
    ) {
        let curve = table::borrow_mut(&mut storage.curves, curve_id);
        assert!(!curve.is_paused, ECurvePaused);
        assert!(!curve.is_completed, ECurveCompleted);
        
        let sui_amount = coin::value(&sui_payment);
        assert!(sui_amount > 0, EInvalidAmount);
        
        // Calculate tokens to receive
        let tokens_out = calculate_buy_amount(curve, sui_amount);
        assert!(tokens_out >= min_tokens_out, ESlippageExceeded);
        assert!(tokens_out <= curve.token_reserve, EInsufficientLiquidity);
        
        // Update curve state
        curve.token_reserve = curve.token_reserve - tokens_out;
        curve.circulating_supply = curve.circulating_supply + tokens_out;
        balance::join(&mut curve.sui_reserve, coin::into_balance(sui_payment));
        
        // Update trading stats
        curve.total_volume = curve.total_volume + (sui_amount as u128);
        curve.buy_count = curve.buy_count + 1;
        curve.last_price = calculate_current_price(curve);
        
        // Check if curve should complete
        let should_complete = curve.circulating_supply >= curve.target_supply;
        if (should_complete) {
            curve.is_completed = true;
            curve.completed_at = clock::timestamp_ms(clock);
            
            // Emit completion event
            events::emit_curve_completed(
                object::id_to_address(&curve_id),
                curve.circulating_supply,
                balance::value(&curve.sui_reserve),
                clock::timestamp_ms(clock)
            );
        };
        
        // Update global stats
        storage.total_volume = storage.total_volume + (sui_amount as u128);
        
        // Emit buy event (v2 simplified)
        events::emit_token_bought(
            object::id_to_address(&curve_id),
            object::id_to_address(&curve.token_id),
            tx_context::sender(ctx),
            sui_amount,
            tokens_out,
            curve.last_price,
            0, // price_impact_bps (simplified for v2)
            curve.circulating_supply,
            clock::timestamp_ms(clock)
        );
    }
    
    /// Sell tokens to the bonding curve - ENTRY FUNCTION
    public entry fun sell_tokens_entry(
        storage: &mut BondingCurveStorage,
        curve_id: ID,
        token_amount: u64,
        min_sui_out: u64,
        clock: &Clock,
        ctx: &mut TxContext
    ) {
        let curve = table::borrow_mut(&mut storage.curves, curve_id);
        assert!(!curve.is_paused, ECurvePaused);
        assert!(!curve.is_completed, ECurveCompleted);
        assert!(token_amount > 0, EInvalidAmount);
        
        // Calculate SUI to receive
        let sui_out = calculate_sell_amount(curve, token_amount);
        assert!(sui_out >= min_sui_out, ESlippageExceeded);
        assert!(sui_out <= balance::value(&curve.sui_reserve), EInsufficientLiquidity);
        
        // Update curve state
        curve.token_reserve = curve.token_reserve + token_amount;
        curve.circulating_supply = curve.circulating_supply - token_amount;
        let sui_payment = coin::take(&mut curve.sui_reserve, sui_out, ctx);
        
        // Update trading stats
        curve.total_volume = curve.total_volume + (sui_out as u128);
        curve.sell_count = curve.sell_count + 1;
        curve.last_price = calculate_current_price(curve);
        
        // Update global stats
        storage.total_volume = storage.total_volume + (sui_out as u128);
        
        // Transfer SUI to seller
        transfer::public_transfer(sui_payment, tx_context::sender(ctx));
        
        // Emit sell event (v2 simplified)
        events::emit_token_sold(
            object::id_to_address(&curve_id),
            object::id_to_address(&curve.token_id),
            tx_context::sender(ctx),
            token_amount,
            sui_out,
            curve.last_price,
            0, // price_impact_bps (simplified for v2)
            curve.circulating_supply,
            clock::timestamp_ms(clock)
        );
    }
    
    // ======== Helper Functions (Called from factory) ========
    
    public fun buy_tokens(
        storage: &mut BondingCurveStorage,
        curve_id: ID,
        sui_payment: Coin<SUI>,
        min_tokens: u64,
        clock: &Clock,
        ctx: &mut TxContext
    ) {
        let curve = table::borrow_mut(&mut storage.curves, curve_id);
        assert!(!curve.is_paused, ECurvePaused);
        assert!(!curve.is_completed, ECurveCompleted);
        
        let sui_amount = coin::value(&sui_payment);
        assert!(sui_amount > 0, EInvalidAmount);
        
        // Calculate tokens to receive
        let tokens_out = calculate_buy_amount(curve, sui_amount);
        assert!(tokens_out >= min_tokens, ESlippageExceeded);
        assert!(tokens_out <= curve.token_reserve, EInsufficientLiquidity);
        
        // Update curve state
        curve.token_reserve = curve.token_reserve - tokens_out;
        curve.circulating_supply = curve.circulating_supply + tokens_out;
        balance::join(&mut curve.sui_reserve, coin::into_balance(sui_payment));
        
        // Update trading stats
        curve.total_volume = curve.total_volume + (sui_amount as u128);
        curve.buy_count = curve.buy_count + 1;
        curve.last_price = calculate_current_price(curve);
        
        // Check if curve should complete
        let should_complete = curve.circulating_supply >= curve.target_supply;
        if (should_complete) {
            curve.is_completed = true;
            curve.completed_at = clock::timestamp_ms(clock);
            
            // Emit completion event
            events::emit_curve_completed(
                object::id_to_address(&curve_id),
                curve.circulating_supply,
                balance::value(&curve.sui_reserve),
                clock::timestamp_ms(clock)
            );
        };
        
        // Update global stats
        storage.total_volume = storage.total_volume + (sui_amount as u128);
        
        // Emit buy event
        events::emit_token_bought(
            object::id_to_address(&curve_id),
            object::id_to_address(&curve.token_id),
            tx_context::sender(ctx),
            sui_amount,
            tokens_out,
            curve.last_price,
            0,
            curve.circulating_supply,
            clock::timestamp_ms(clock)
        );
    }
    
    public fun sell_tokens(
        storage: &mut BondingCurveStorage,
        curve_id: ID,
        token_amount: u64,
        min_sui: u64,
        clock: &Clock,
        ctx: &mut TxContext
    ) {
        let curve = table::borrow_mut(&mut storage.curves, curve_id);
        assert!(!curve.is_paused, ECurvePaused);
        assert!(!curve.is_completed, ECurveCompleted);
        assert!(token_amount > 0, EInvalidAmount);
        
        // Calculate SUI to receive
        let sui_out = calculate_sell_amount(curve, token_amount);
        assert!(sui_out >= min_sui, ESlippageExceeded);
        assert!(sui_out <= balance::value(&curve.sui_reserve), EInsufficientLiquidity);
        
        // Update curve state
        curve.token_reserve = curve.token_reserve + token_amount;
        curve.circulating_supply = curve.circulating_supply - token_amount;
        let sui_payment = coin::take(&mut curve.sui_reserve, sui_out, ctx);
        
        // Update trading stats
        curve.total_volume = curve.total_volume + (sui_out as u128);
        curve.sell_count = curve.sell_count + 1;
        curve.last_price = calculate_current_price(curve);
        
        // Update global stats
        storage.total_volume = storage.total_volume + (sui_out as u128);
        
        // Transfer SUI to seller
        transfer::public_transfer(sui_payment, tx_context::sender(ctx));
        
        // Emit sell event
        events::emit_token_sold(
            object::id_to_address(&curve_id),
            object::id_to_address(&curve.token_id),
            tx_context::sender(ctx),
            token_amount,
            sui_out,
            curve.last_price,
            0,
            curve.circulating_supply,
            clock::timestamp_ms(clock)
        );
    }
    
    // ======== Calculation Functions ========
    
    fun calculate_buy_amount(curve: &CurveData, sui_amount: u64): u64 {
        // Simplified bonding curve formula
        // Price increases as supply increases
        let current_price = calculate_current_price(curve);
        let avg_price = current_price + (curve.curve_factor * sui_amount / 2);
        
        if (avg_price == 0) {
            return 0
        };
        
        // tokens = sui_amount / average_price
        (sui_amount * 1_000_000) / avg_price
    }
    
    fun calculate_sell_amount(curve: &CurveData, token_amount: u64): u64 {
        // Calculate SUI to return for selling tokens
        let current_price = calculate_current_price(curve);
        let avg_price = current_price - (curve.curve_factor * token_amount / 2);
        
        if (avg_price < curve.base_price) {
            return (token_amount * curve.base_price) / 1_000_000
        };
        
        // sui_amount = tokens * average_price
        (token_amount * avg_price) / 1_000_000
    }
    
    fun calculate_current_price(curve: &CurveData): u64 {
        // Linear bonding curve: price = base_price + (curve_factor * circulating_supply / total_supply)
        let supply_ratio = if (curve.total_supply > 0) {
            (curve.circulating_supply * 10000) / curve.total_supply
        } else {
            0
        };
        
        curve.base_price + (curve.curve_factor * supply_ratio / 100)
    }
    
    fun complete_curve(
        storage: &mut BondingCurveStorage,
        curve_id: ID,
        clock: &Clock
    ) {
        let curve = table::borrow_mut(&mut storage.curves, curve_id);
        curve.is_completed = true;
        curve.completed_at = clock::timestamp_ms(clock);
        
        // Emit completion event
        events::emit_curve_completed(
            object::id_to_address(&curve_id),
            curve.circulating_supply,
            balance::value(&curve.sui_reserve),
            clock::timestamp_ms(clock)
        );
    }
    
    // ======== View Functions ========
    
    public fun get_curve_info(storage: &BondingCurveStorage, curve_id: ID): (
        u64, // current_price
        u64, // circulating_supply
        u64, // token_reserve
        u64, // sui_reserve
        bool, // is_completed
        bool  // is_paused
    ) {
        assert!(table::contains(&storage.curves, curve_id), ECurveNotFound);
        let curve = table::borrow(&storage.curves, curve_id);
        
        (
            calculate_current_price(curve),
            curve.circulating_supply,
            curve.token_reserve,
            balance::value(&curve.sui_reserve),
            curve.is_completed,
            curve.is_paused
        )
    }
    
    public fun get_buy_quote(
        storage: &BondingCurveStorage,
        curve_id: ID,
        sui_amount: u64
    ): u64 {
        assert!(table::contains(&storage.curves, curve_id), ECurveNotFound);
        let curve = table::borrow(&storage.curves, curve_id);
        calculate_buy_amount(curve, sui_amount)
    }
    
    public fun get_sell_quote(
        storage: &BondingCurveStorage,
        curve_id: ID,
        token_amount: u64
    ): u64 {
        assert!(table::contains(&storage.curves, curve_id), ECurveNotFound);
        let curve = table::borrow(&storage.curves, curve_id);
        calculate_sell_amount(curve, token_amount)
    }
}