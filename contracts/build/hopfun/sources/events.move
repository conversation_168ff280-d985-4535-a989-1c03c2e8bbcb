module hopfun::events {
    use sui::event;
    use std::string::String;
    
    // ======== Token Events ========
    
    /// Emitted when a new token is created
    struct TokenCreated has copy, drop {
        token_id: address,
        curve_id: address,
        creator: address,
        name: String,
        symbol: String,
        total_supply: u64,
        decimals: u8,
        initial_price: u64,
        timestamp: u64
    }
    
    /// Emitted when token metadata is updated
    struct TokenMetadataUpdated has copy, drop {
        token_id: address,
        description: String,
        image_url: String,
        twitter: String,
        website: String,
        telegram: String,
        timestamp: u64
    }
    
    // ======== Bonding Curve Events ========
    
    /// Emitted when a bonding curve is created
    struct BondingCurveCreated has copy, drop {
        curve_id: address,
        token_id: address,
        base_price: u64,
        curve_factor: u64,
        target_supply: u64,
        timestamp: u64
    }
    
    /// Emitted when bonding curve completes and migrates to DEX
    struct BondingCurveCompleted has copy, drop {
        curve_id: address,
        token_id: address,
        final_supply: u64,
        final_price: u64,
        total_sui_raised: u64,
        dex_pool_id: address,
        timestamp: u64
    }
    
    /// Emitted when bonding curve progress is updated
    struct BondingCurveProgress has copy, drop {
        curve_id: address,
        current_supply: u64,
        target_supply: u64,
        progress_bps: u64, // Progress in basis points (0-10000)
        timestamp: u64
    }
    
    // ======== Trading Events ========
    
    /// Emitted when tokens are bought
    struct TokenBought has copy, drop {
        curve_id: address,
        token_id: address,
        buyer: address,
        sui_amount: u64,
        token_amount: u64,
        price_per_token: u64,
        price_impact_bps: u64,
        new_supply: u64,
        timestamp: u64
    }
    
    /// Emitted when tokens are sold
    struct TokenSold has copy, drop {
        curve_id: address,
        token_id: address,
        seller: address,
        token_amount: u64,
        sui_amount: u64,
        price_per_token: u64,
        price_impact_bps: u64,
        new_supply: u64,
        timestamp: u64
    }
    
    /// Emitted when a trade fails
    struct TradeFailed has copy, drop {
        curve_id: address,
        trader: address,
        reason: String,
        timestamp: u64
    }
    
    // ======== DEX Events ========
    
    /// Emitted when liquidity is added to DEX
    struct LiquidityAdded has copy, drop {
        pool_id: address,
        token_id: address,
        provider: address,
        token_amount: u64,
        sui_amount: u64,
        lp_tokens_minted: u64,
        timestamp: u64
    }
    
    /// Emitted when liquidity is removed from DEX
    struct LiquidityRemoved has copy, drop {
        pool_id: address,
        token_id: address,
        provider: address,
        lp_tokens_burned: u64,
        token_amount: u64,
        sui_amount: u64,
        timestamp: u64
    }
    
    // ======== Admin Events ========
    
    /// Emitted when emergency pause is activated
    struct EmergencyPauseActivated has copy, drop {
        pauser: address,
        reason: String,
        timestamp: u64
    }
    
    /// Emitted when emergency pause is deactivated
    struct EmergencyPauseDeactivated has copy, drop {
        unpauser: address,
        timestamp: u64
    }
    
    /// Emitted when admin role is transferred
    struct AdminTransferred has copy, drop {
        old_admin: address,
        new_admin: address,
        timestamp: u64
    }
    
    /// Emitted when fees are collected
    struct FeesCollected has copy, drop {
        collector: address,
        amount: u64,
        timestamp: u64
    }
    
    // ======== Platform Events ========
    
    /// Emitted when platform is initialized
    struct PlatformInitialized has copy, drop {
        admin: address,
        creation_fee: u64
    }
    
    /// Emitted when a curve is created (simplified for v2)
    struct CurveCreated has copy, drop {
        curve_id: address,
        token_id: address,
        creator: address,
        total_supply: u64,
        base_price: u64,
        timestamp: u64
    }
    
    /// Emitted when a curve completes (simplified for v2)
    struct CurveCompleted has copy, drop {
        curve_id: address,
        circulating_supply: u64,
        sui_reserve: u64,
        timestamp: u64
    }
    
    // ======== Event Emission Functions ========
    
    public fun emit_platform_initialized(admin: address, creation_fee: u64) {
        event::emit(PlatformInitialized {
            admin,
            creation_fee
        });
    }
    
    public fun emit_curve_created(
        curve_id: address,
        token_id: address,
        creator: address,
        total_supply: u64,
        base_price: u64,
        timestamp: u64
    ) {
        event::emit(CurveCreated {
            curve_id,
            token_id,
            creator,
            total_supply,
            base_price,
            timestamp
        });
    }
    
    public fun emit_curve_completed(
        curve_id: address,
        circulating_supply: u64,
        sui_reserve: u64,
        timestamp: u64
    ) {
        event::emit(CurveCompleted {
            curve_id,
            circulating_supply,
            sui_reserve,
            timestamp
        });
    }
    
    public fun emit_emergency_pause(admin: address) {
        use std::string;
        emit_emergency_pause_activated(admin, string::utf8(b"Admin initiated pause"), 0);
    }
    
    public fun emit_emergency_unpause(admin: address) {
        emit_emergency_pause_deactivated(admin, 0);
    }
    
    public fun emit_token_created(
        token_id: address,
        curve_id: address,
        creator: address,
        name: String,
        symbol: String,
        total_supply: u64,
        decimals: u8,
        initial_price: u64,
        timestamp: u64
    ) {
        event::emit(TokenCreated {
            token_id,
            curve_id,
            creator,
            name,
            symbol,
            total_supply,
            decimals,
            initial_price,
            timestamp
        });
    }
    
    public fun emit_token_metadata_updated(
        token_id: address,
        description: String,
        image_url: String,
        twitter: String,
        website: String,
        telegram: String,
        timestamp: u64
    ) {
        event::emit(TokenMetadataUpdated {
            token_id,
            description,
            image_url,
            twitter,
            website,
            telegram,
            timestamp
        });
    }
    
    public fun emit_bonding_curve_created(
        curve_id: address,
        token_id: address,
        base_price: u64,
        curve_factor: u64,
        target_supply: u64,
        timestamp: u64
    ) {
        event::emit(BondingCurveCreated {
            curve_id,
            token_id,
            base_price,
            curve_factor,
            target_supply,
            timestamp
        });
    }
    
    public fun emit_bonding_curve_completed(
        curve_id: address,
        token_id: address,
        final_supply: u64,
        final_price: u64,
        total_sui_raised: u64,
        dex_pool_id: address,
        timestamp: u64
    ) {
        event::emit(BondingCurveCompleted {
            curve_id,
            token_id,
            final_supply,
            final_price,
            total_sui_raised,
            dex_pool_id,
            timestamp
        });
    }
    
    public fun emit_bonding_curve_progress(
        curve_id: address,
        current_supply: u64,
        target_supply: u64,
        progress_bps: u64,
        timestamp: u64
    ) {
        event::emit(BondingCurveProgress {
            curve_id,
            current_supply,
            target_supply,
            progress_bps,
            timestamp
        });
    }
    
    public fun emit_token_bought(
        curve_id: address,
        token_id: address,
        buyer: address,
        sui_amount: u64,
        token_amount: u64,
        price_per_token: u64,
        price_impact_bps: u64,
        new_supply: u64,
        timestamp: u64
    ) {
        event::emit(TokenBought {
            curve_id,
            token_id,
            buyer,
            sui_amount,
            token_amount,
            price_per_token,
            price_impact_bps,
            new_supply,
            timestamp
        });
    }
    
    public fun emit_token_sold(
        curve_id: address,
        token_id: address,
        seller: address,
        token_amount: u64,
        sui_amount: u64,
        price_per_token: u64,
        price_impact_bps: u64,
        new_supply: u64,
        timestamp: u64
    ) {
        event::emit(TokenSold {
            curve_id,
            token_id,
            seller,
            token_amount,
            sui_amount,
            price_per_token,
            price_impact_bps,
            new_supply,
            timestamp
        });
    }
    
    public fun emit_trade_failed(
        curve_id: address,
        trader: address,
        reason: String,
        timestamp: u64
    ) {
        event::emit(TradeFailed {
            curve_id,
            trader,
            reason,
            timestamp
        });
    }
    
    public fun emit_liquidity_added(
        pool_id: address,
        token_id: address,
        provider: address,
        token_amount: u64,
        sui_amount: u64,
        lp_tokens_minted: u64,
        timestamp: u64
    ) {
        event::emit(LiquidityAdded {
            pool_id,
            token_id,
            provider,
            token_amount,
            sui_amount,
            lp_tokens_minted,
            timestamp
        });
    }
    
    public fun emit_liquidity_removed(
        pool_id: address,
        token_id: address,
        provider: address,
        lp_tokens_burned: u64,
        token_amount: u64,
        sui_amount: u64,
        timestamp: u64
    ) {
        event::emit(LiquidityRemoved {
            pool_id,
            token_id,
            provider,
            lp_tokens_burned,
            token_amount,
            sui_amount,
            timestamp
        });
    }
    
    public fun emit_emergency_pause_activated(
        pauser: address,
        reason: String,
        timestamp: u64
    ) {
        event::emit(EmergencyPauseActivated {
            pauser,
            reason,
            timestamp
        });
    }
    
    public fun emit_emergency_pause_deactivated(
        unpauser: address,
        timestamp: u64
    ) {
        event::emit(EmergencyPauseDeactivated {
            unpauser,
            timestamp
        });
    }
    
    public fun emit_admin_transferred(
        old_admin: address,
        new_admin: address,
        timestamp: u64
    ) {
        event::emit(AdminTransferred {
            old_admin,
            new_admin,
            timestamp
        });
    }
    
    public fun emit_fees_collected(
        collector: address,
        amount: u64,
        timestamp: u64
    ) {
        event::emit(FeesCollected {
            collector,
            amount,
            timestamp
        });
    }
}