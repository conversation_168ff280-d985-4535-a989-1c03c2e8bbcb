#[allow(lint(custom_state_change))]
module hopfun::meme_coin_factory_v2 {
    use sui::object::{Self, UID, ID};
    use sui::tx_context::{Self, TxContext};
    use sui::transfer;
    use sui::coin::{Self, Coin, TreasuryCap, CoinMetadata};
    use sui::url::{Self, Url};
    use sui::balance::{Self, Balance};
    use sui::sui::SUI;
    use sui::clock::{Self, Clock};
    use sui::table::{Self, Table};
    use sui::dynamic_field as df;
    use std::string::{Self, String};
    use std::vector;
    use std::option::{Self, Option};
    use std::type_name::{Self, TypeName};
    
    // use hopfun::errors; // Commented out - using local error constants instead
    use hopfun::events;
    use hopfun::bonding_curve_v2;
    
    // ======== Errors ========
    const EInvalidTokenData: u64 = 1001;
    const ETokenPoolExhausted: u64 = 1002;
    const ETokenAlreadyExists: u64 = 1003;
    const ERateLimitExceeded: u64 = 1004;
    const EUnauthorized: u64 = 1005;
    const EInvalidBatchSize: u64 = 1006;
    
    // ======== Constants ========
    const DEFAULT_CREATION_FEE: u64 = 100_000_000; // 0.1 SUI
    const DEFAULT_TRADING_FEE_BPS: u64 = 30; // 0.3%
    const MAX_BATCH_SIZE: u64 = 10;
    const RATE_LIMIT_TOKENS_PER_DAY: u64 = 10;
    const RATE_LIMIT_PERIOD_MS: u64 = 86_400_000; // 24 hours
    
    // ======== Structs ========
    
    /// Admin capability for platform management
    struct AdminCap has key, store {
        id: UID
    }
    
    /// Platform configuration with enhanced features
    struct PlatformConfigV2 has key {
        id: UID,
        admin: address,
        fee_recipient: address,
        creation_fee: u64,
        trading_fee_bps: u64,
        emergency_paused: bool,
        total_tokens_created: u64,
        total_fees_collected: Balance<SUI>,
        // New fields for v2
        rate_limiting_enabled: bool,
        max_tokens_per_user: u64,
    }
    
    /// Enhanced token registry with dynamic storage
    struct TokenRegistryV2 has key {
        id: UID,
        token_count: u64,
        // Use dynamic fields for scalable storage
        // tokens stored as df::add(&mut id, token_id, TokenInfo)
    }
    
    /// Token information stored in registry
    struct TokenInfo has store, drop {
        creator: address,
        name: String,
        symbol: String,
        created_at: u64,
        bonding_curve_id: ID,
        metadata_id: ID,
        is_active: bool,
        total_supply: u64,
        decimals: u8,
    }
    
    /// Meme token metadata with enhanced features
    struct MemeTokenMetadataV2 has key, store {
        id: UID,
        token_id: ID,
        curve_id: ID,
        creator: address,
        name: String,
        symbol: String,
        description: String,
        decimals: u8,
        total_supply: u64,
        image_url: String,
        twitter: String,
        website: String,
        telegram: String,
        created_at: u64,
        is_graduated: bool,
        // New fields for v2
        trading_volume: u64,
        holder_count: u64,
        last_price: u64,
    }
    
    /// Rate limiting structure
    struct RateLimiter has store {
        tokens_created: u64,
        last_reset: u64,
    }
    
    /// Token creation parameters for batch operations
    struct TokenCreationParams has drop {
        name: vector<u8>,
        symbol: vector<u8>,
        description: vector<u8>,
        decimals: u8,
        total_supply: u64,
        image_url: vector<u8>,
        twitter: vector<u8>,
        website: vector<u8>,
        telegram: vector<u8>,
    }
    
    /// Generic meme token type for dynamic creation
    struct MemeToken<phantom T> has drop {}
    
    // ======== Initialization ========
    
    fun init(ctx: &mut TxContext) {
        let admin = tx_context::sender(ctx);
        
        // Create admin capability
        let admin_cap = AdminCap {
            id: object::new(ctx)
        };
        
        // Create enhanced platform configuration
        let config = PlatformConfigV2 {
            id: object::new(ctx),
            admin,
            fee_recipient: admin,
            creation_fee: DEFAULT_CREATION_FEE,
            trading_fee_bps: DEFAULT_TRADING_FEE_BPS,
            emergency_paused: false,
            total_tokens_created: 0,
            total_fees_collected: balance::zero<SUI>(),
            rate_limiting_enabled: true,
            max_tokens_per_user: 100,
        };
        
        // Create enhanced token registry
        let registry = TokenRegistryV2 {
            id: object::new(ctx),
            token_count: 0,
        };
        
        // Transfer objects
        transfer::transfer(admin_cap, admin);
        transfer::share_object(config);
        transfer::share_object(registry);
        
        // Emit initialization event
        events::emit_platform_initialized(admin, DEFAULT_CREATION_FEE);
    }
    
    // ======== Entry Functions (Directly Callable from Frontend) ========
    
    /// Create a new meme token - ENTRY FUNCTION for direct frontend calls
    public entry fun create_meme_token_entry(
        config: &mut PlatformConfigV2,
        registry: &mut TokenRegistryV2,
        storage: &mut bonding_curve_v2::BondingCurveStorage,
        name: vector<u8>,
        symbol: vector<u8>,
        description: vector<u8>,
        decimals: u8,
        total_supply: u64,
        image_url: vector<u8>,
        twitter: vector<u8>,
        website: vector<u8>,
        telegram: vector<u8>,
        creation_fee: Coin<SUI>,
        clock: &Clock,
        ctx: &mut TxContext
    ) {
        // Check emergency pause
        assert!(!config.emergency_paused, 999); // Emergency paused error
        
        // Validate inputs
        validate_token_params(&name, &symbol, &description, decimals, total_supply);
        validate_social_links(&image_url, &twitter, &website, &telegram);
        
        // Check and update rate limiting
        check_rate_limit(config, registry, tx_context::sender(ctx), clock);
        
        // Check creation fee
        let fee_amount = coin::value(&creation_fee);
        assert!(fee_amount >= config.creation_fee, 1000); // Insufficient balance error
        
        // Add fee to platform balance
        let fee_balance = coin::into_balance(creation_fee);
        balance::join(&mut config.total_fees_collected, fee_balance);
        
        // Create unique token ID
        let token_uid = object::new(ctx);
        let token_id = object::uid_to_inner(&token_uid);
        
        // Create bonding curve for this token
        let curve_id = bonding_curve_v2::create_curve(
            storage,
            token_id,
            total_supply,
            decimals,
            clock,
            ctx
        );
        
        // Create metadata object
        let metadata = MemeTokenMetadataV2 {
            id: token_uid,
            token_id,
            curve_id,
            creator: tx_context::sender(ctx),
            name: string::utf8(name),
            symbol: string::utf8(symbol),
            description: string::utf8(description),
            decimals,
            total_supply,
            image_url: string::utf8(image_url),
            twitter: string::utf8(twitter),
            website: string::utf8(website),
            telegram: string::utf8(telegram),
            created_at: clock::timestamp_ms(clock),
            is_graduated: false,
            trading_volume: 0,
            holder_count: 0,
            last_price: 0,
        };
        
        // Create token info for registry
        let token_info = TokenInfo {
            creator: tx_context::sender(ctx),
            name: string::utf8(name),
            symbol: string::utf8(symbol),
            created_at: clock::timestamp_ms(clock),
            bonding_curve_id: curve_id,
            metadata_id: token_id,
            is_active: true,
            total_supply,
            decimals,
        };
        
        // Store token info in registry using dynamic fields
        df::add(&mut registry.id, token_id, token_info);
        registry.token_count = registry.token_count + 1;
        
        // Update platform stats
        config.total_tokens_created = config.total_tokens_created + 1;
        
        // Transfer metadata to creator
        transfer::transfer(metadata, tx_context::sender(ctx));
        
        // Emit creation event
        events::emit_token_created(
            object::id_to_address(&token_id),
            object::id_to_address(&curve_id),
            tx_context::sender(ctx),
            string::utf8(name),
            string::utf8(symbol),
            total_supply,
            decimals,
            0,
            clock::timestamp_ms(clock)
        );
    }
    
    // Batch operations are not supported in entry functions with vector parameters
    // This function can be called from other modules but not directly from frontend
    // /// Create multiple tokens in a single transaction - BATCH FUNCTION
    // public fun create_multiple_tokens(
    //     config: &mut PlatformConfigV2,
    //     registry: &mut TokenRegistryV2,
    //     storage: &mut bonding_curve_v2::BondingCurveStorage,
    //     tokens_data: vector<TokenCreationParams>,
    //     total_fee: Coin<SUI>,
    //     clock: &Clock,
    //     ctx: &mut TxContext
    // ) {
    //     let batch_size = vector::length(&tokens_data);
    //     assert!(batch_size > 0 && batch_size <= MAX_BATCH_SIZE, EInvalidBatchSize);
        
    //     // Check emergency pause
    //     assert!(!config.emergency_paused, 999); // Emergency paused error
        
    //     // Check total fee
    //     let required_fee = config.creation_fee * batch_size;
    //     assert!(coin::value(&total_fee) >= required_fee, 1000); // Insufficient balance error
        
    //     // Process fee
    //     let fee_balance = coin::into_balance(total_fee);
    //     balance::join(&mut config.total_fees_collected, fee_balance);
        
    //     // Create each token
    //     let i = 0;
    //     while (i < batch_size) {
    //         let token_data = vector::borrow(&tokens_data, i);
    //         create_token_internal(
    //             config,
    //             registry,
    //             storage,
    //             token_data,
    //             clock,
    //             ctx
    //         );
    //         i = i + 1;
    //     };
    // }
    
    /// Buy tokens from bonding curve - ENTRY FUNCTION
    public entry fun buy_token(
        registry: &TokenRegistryV2,
        storage: &mut bonding_curve_v2::BondingCurveStorage,
        token_id: ID,
        sui_amount: Coin<SUI>,
        min_tokens: u64,
        clock: &Clock,
        ctx: &mut TxContext
    ) {
        // Get token info from registry
        assert!(df::exists_(&registry.id, token_id), ETokenAlreadyExists);
        let token_info: &TokenInfo = df::borrow(&registry.id, token_id);
        
        // Execute buy on bonding curve
        bonding_curve_v2::buy_tokens(
            storage,
            token_info.bonding_curve_id,
            sui_amount,
            min_tokens,
            clock,
            ctx
        );
    }
    
    /// Sell tokens to bonding curve - ENTRY FUNCTION
    public entry fun sell_token(
        registry: &TokenRegistryV2,
        storage: &mut bonding_curve_v2::BondingCurveStorage,
        token_id: ID,
        token_amount: u64,
        min_sui: u64,
        clock: &Clock,
        ctx: &mut TxContext
    ) {
        // Get token info from registry
        assert!(df::exists_(&registry.id, token_id), ETokenAlreadyExists);
        let token_info: &TokenInfo = df::borrow(&registry.id, token_id);
        
        // Execute sell on bonding curve
        bonding_curve_v2::sell_tokens(
            storage,
            token_info.bonding_curve_id,
            token_amount,
            min_sui,
            clock,
            ctx
        );
    }
    
    // ======== Internal Functions ========
    
    fun create_token_internal(
        config: &mut PlatformConfigV2,
        registry: &mut TokenRegistryV2,
        storage: &mut bonding_curve_v2::BondingCurveStorage,
        params: &TokenCreationParams,
        clock: &Clock,
        ctx: &mut TxContext
    ) {
        // Similar logic to create_meme_token_entry but without fee handling
        // Used for batch operations
        
        // Validate inputs
        validate_token_params(&params.name, &params.symbol, &params.description, params.decimals, params.total_supply);
        
        // Create unique token ID
        let token_uid = object::new(ctx);
        let token_id = object::uid_to_inner(&token_uid);
        
        // Create bonding curve
        let curve_id = bonding_curve_v2::create_curve(
            storage,
            token_id,
            params.total_supply,
            params.decimals,
            clock,
            ctx
        );
        
        // Create metadata
        let metadata = MemeTokenMetadataV2 {
            id: token_uid,
            token_id,
            curve_id,
            creator: tx_context::sender(ctx),
            name: string::utf8(params.name),
            symbol: string::utf8(params.symbol),
            description: string::utf8(params.description),
            decimals: params.decimals,
            total_supply: params.total_supply,
            image_url: string::utf8(params.image_url),
            twitter: string::utf8(params.twitter),
            website: string::utf8(params.website),
            telegram: string::utf8(params.telegram),
            created_at: clock::timestamp_ms(clock),
            is_graduated: false,
            trading_volume: 0,
            holder_count: 0,
            last_price: 0,
        };
        
        // Store in registry
        let token_info = TokenInfo {
            creator: tx_context::sender(ctx),
            name: string::utf8(params.name),
            symbol: string::utf8(params.symbol),
            created_at: clock::timestamp_ms(clock),
            bonding_curve_id: curve_id,
            metadata_id: token_id,
            is_active: true,
            total_supply: params.total_supply,
            decimals: params.decimals,
        };
        
        df::add(&mut registry.id, token_id, token_info);
        registry.token_count = registry.token_count + 1;
        config.total_tokens_created = config.total_tokens_created + 1;
        
        // Transfer metadata
        transfer::transfer(metadata, tx_context::sender(ctx));
    }
    
    fun check_rate_limit(
        config: &PlatformConfigV2,
        registry: &mut TokenRegistryV2,
        user: address,
        clock: &Clock
    ) {
        if (!config.rate_limiting_enabled) {
            return
        };
        
        let current_time = clock::timestamp_ms(clock);
        
        // Check if user has rate limiter
        if (!df::exists_(&registry.id, user)) {
            // Create new rate limiter for user
            let limiter = RateLimiter {
                tokens_created: 0,
                last_reset: current_time,
            };
            df::add(&mut registry.id, user, limiter);
        };
        
        // Get and update rate limiter
        let limiter: &mut RateLimiter = df::borrow_mut(&mut registry.id, user);
        
        // Reset if period has passed
        if (current_time - limiter.last_reset > RATE_LIMIT_PERIOD_MS) {
            limiter.tokens_created = 0;
            limiter.last_reset = current_time;
        };
        
        // Check rate limit
        assert!(limiter.tokens_created < RATE_LIMIT_TOKENS_PER_DAY, ERateLimitExceeded);
        limiter.tokens_created = limiter.tokens_created + 1;
    }
    
    // ======== Validation Functions ========
    
    fun validate_token_params(
        name: &vector<u8>,
        symbol: &vector<u8>,
        description: &vector<u8>,
        decimals: u8,
        total_supply: u64
    ) {
        assert!(vector::length(name) > 0 && vector::length(name) <= 32, EInvalidTokenData);
        assert!(vector::length(symbol) > 0 && vector::length(symbol) <= 10, EInvalidTokenData);
        assert!(vector::length(description) <= 500, EInvalidTokenData);
        assert!(decimals <= 18, EInvalidTokenData);
        assert!(total_supply > 0 && total_supply <= 1_000_000_000_000_000, EInvalidTokenData);
    }
    
    fun validate_social_links(
        image_url: &vector<u8>,
        twitter: &vector<u8>,
        website: &vector<u8>,
        telegram: &vector<u8>
    ) {
        assert!(vector::length(image_url) <= 200, EInvalidTokenData);
        assert!(vector::length(twitter) <= 200, EInvalidTokenData);
        assert!(vector::length(website) <= 200, EInvalidTokenData);
        assert!(vector::length(telegram) <= 200, EInvalidTokenData);
    }
    
    // ======== Admin Functions ========
    
    public entry fun update_config(
        _admin_cap: &AdminCap,
        config: &mut PlatformConfigV2,
        creation_fee: Option<u64>,
        trading_fee_bps: Option<u64>,
        fee_recipient: Option<address>,
        rate_limiting_enabled: Option<bool>,
        max_tokens_per_user: Option<u64>
    ) {
        if (option::is_some(&creation_fee)) {
            config.creation_fee = *option::borrow(&creation_fee);
        };
        
        if (option::is_some(&trading_fee_bps)) {
            config.trading_fee_bps = *option::borrow(&trading_fee_bps);
        };
        
        if (option::is_some(&fee_recipient)) {
            config.fee_recipient = *option::borrow(&fee_recipient);
        };
        
        if (option::is_some(&rate_limiting_enabled)) {
            config.rate_limiting_enabled = *option::borrow(&rate_limiting_enabled);
        };
        
        if (option::is_some(&max_tokens_per_user)) {
            config.max_tokens_per_user = *option::borrow(&max_tokens_per_user);
        };
    }
    
    public entry fun emergency_pause(_admin_cap: &AdminCap, config: &mut PlatformConfigV2, ctx: &mut TxContext) {
        config.emergency_paused = true;
        events::emit_emergency_pause(tx_context::sender(ctx));
    }
    
    public entry fun emergency_unpause(_admin_cap: &AdminCap, config: &mut PlatformConfigV2, ctx: &mut TxContext) {
        config.emergency_paused = false;
        events::emit_emergency_unpause(tx_context::sender(ctx));
    }
    
    public entry fun collect_fees(
        _admin_cap: &AdminCap,
        config: &mut PlatformConfigV2,
        ctx: &mut TxContext
    ) {
        let amount = balance::value(&config.total_fees_collected);
        if (amount > 0) {
            let fees = coin::take(&mut config.total_fees_collected, amount, ctx);
            transfer::public_transfer(fees, config.fee_recipient);
        };
    }
    
    // ======== View Functions ========
    
    public fun get_token_info(registry: &TokenRegistryV2, token_id: ID): &TokenInfo {
        assert!(df::exists_(&registry.id, token_id), ETokenAlreadyExists);
        df::borrow(&registry.id, token_id)
    }
    
    public fun get_platform_stats(config: &PlatformConfigV2): (u64, u64, u64) {
        (
            config.total_tokens_created,
            balance::value(&config.total_fees_collected),
            config.creation_fee
        )
    }
    
    public fun is_paused(config: &PlatformConfigV2): bool {
        config.emergency_paused
    }
}