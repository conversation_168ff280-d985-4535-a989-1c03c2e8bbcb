{"network": "devnet", "deployedAt": "2025-08-26T12:00:00Z", "deployer": "0x36d006d385a88e5fb830c68db3c9ac927f5cbe0b309c67bad7227c1a2352fe67", "packageId": "0x12aca1986e7d431d1c0a8e65b87401a5a5753ffcb91be6c55ca914b9d7819f91", "transactionDigest": "", "objects": {"adminCap": "0x4aa2df7674cbf8e27ac75689800dcb920f00365b7531a50cde4c58051d09f539", "platformConfigV2": "0x77b0e8f58bc51e88e3b388c982e953d99d696e4e15e956088e0f67a993f946f7", "tokenRegistryV2": "0xa93e991df37bc4b7c88c8e7a8f64b013a3bc38c965c5bb0a009a5bb6b014b8e8", "bondingCurveStorage": "0xcd9f4610cf2ccd6ccd85149b9350828170d0c51cb7d6f39747a6f9ea4fe898b4", "legacyPlatformConfig": "0x5f09ee35e719e699eeac2ae4503bb7e965a19e996bb577e6fc36b8e88c36c6d5", "legacyTokenRegistry": "0xee5d581a48a73f03d7253cb2967d5a62d055cda3d7dbf5f2ceb329887838be0a"}, "status": "success", "notes": "V2 deployment with entry functions - no witness pattern required", "networkRpcUrl": "https://fullnode.devnet.sui.io:443"}