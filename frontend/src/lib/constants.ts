// Smart Contract Addresses (Devnet) - V2 Deployment
export const PACKAGE_ID = "0x12aca1986e7d431d1c0a8e65b87401a5a5753ffcb91be6c55ca914b9d7819f91";
export const PLATFORM_CONFIG_V2 = "0x77b0e8f58bc51e88e3b388c982e953d99d696e4e15e956088e0f67a993f946f7";
export const TOKEN_REGISTRY_V2 = "0xa93e991df37bc4b7c88c8e7a8f64b013a3bc38c965c5bb0a009a5bb6b014b8e8";
export const BONDING_CURVE_STORAGE = "0xcd9f4610cf2ccd6ccd85149b9350828170d0c51cb7d6f39747a6f9ea4fe898b4";
export const ADMIN_CAP = "0x4aa2df7674cbf8e27ac75689800dcb920f00365b7531a50cde4c58051d09f539";

// Legacy V1 addresses (kept for reference)
export const PLATFORM_CONFIG = "0x5f09ee35e719e699eeac2ae4503bb7e965a19e996bb577e6fc36b8e88c36c6d5";
export const TOKEN_REGISTRY = "0xee5d581a48a73f03d7253cb2967d5a62d055cda3d7dbf5f2ceb329887838be0a";

// Token Creation
export const DEFAULT_CREATION_FEE = 100_000_000; // 0.1 SUI in MIST
export const DEFAULT_TRADING_FEE_BPS = 30; // 0.3%
export const DEFAULT_DECIMALS = 9;
export const DEFAULT_TOTAL_SUPPLY = 1_000_000_000;

// Token Validation
export const MAX_NAME_LENGTH = 32;
export const MAX_SYMBOL_LENGTH = 10;
export const MAX_DESCRIPTION_LENGTH = 500;
export const MAX_URL_LENGTH = 200;
export const MAX_DECIMALS = 18;
export const MIN_SUPPLY = 1000;
export const MAX_SUPPLY = 1_000_000_000_000_000;

// Network Configuration
export const NETWORK = process.env.NEXT_PUBLIC_NETWORK || 'devnet';
export const RPC_URL = process.env.NEXT_PUBLIC_RPC_URL || 'https://fullnode.devnet.sui.io:443';

// UI Constants
export const TOAST_DURATION = 5000;
export const DEBOUNCE_DELAY = 300;
export const PAGE_SIZE = 20;