import { Transaction } from '@mysten/sui/transactions';
import { 
  PACKAGE_ID, 
  PLATFORM_CONFIG_V2,
  TOKEN_REGISTRY_V2,
  BONDING_CURVE_STORAGE,
  DEFAULT_CREATION_FEE 
} from '@/lib/constants';
import { CreateTokenData } from '@/types/token';

/**
 * Creates a transaction for minting a new meme token using the V2 entry function
 * This function can be called directly from the frontend without witness requirements
 */
export async function createMemeTokenTransaction(
  tokenData: CreateTokenData,
  senderAddress: string
): Promise<Transaction> {
  const tx = new Transaction();
  
  // Set sender
  tx.setSender(senderAddress);
  
  // Split coins for creation fee (0.1 SUI)
  const [creationFee] = tx.splitCoins(tx.gas, [DEFAULT_CREATION_FEE]);
  
  // Call the new entry function directly - no witness needed!
  tx.moveCall({
    target: `${PACKAGE_ID}::meme_coin_factory_v2::create_meme_token_entry`,
    arguments: [
      tx.object(PLATFORM_CONFIG_V2), // config: &mut PlatformConfigV2
      tx.object(TOKEN_REGISTRY_V2), // registry: &mut TokenRegistryV2
      tx.object(BONDING_CURVE_STORAGE), // storage: &mut BondingCurveStorage
      tx.pure.vector('u8', Array.from(new TextEncoder().encode(tokenData.name))), // name
      tx.pure.vector('u8', Array.from(new TextEncoder().encode(tokenData.symbol))), // symbol
      tx.pure.vector('u8', Array.from(new TextEncoder().encode(tokenData.description || ''))), // description
      tx.pure.u8(tokenData.decimals), // decimals
      tx.pure.u64(tokenData.totalSupply), // total_supply
      tx.pure.vector('u8', Array.from(new TextEncoder().encode(tokenData.imageUrl || ''))), // image_url
      tx.pure.vector('u8', Array.from(new TextEncoder().encode(tokenData.twitter || ''))), // twitter
      tx.pure.vector('u8', Array.from(new TextEncoder().encode(tokenData.website || ''))), // website
      tx.pure.vector('u8', Array.from(new TextEncoder().encode(tokenData.telegram || ''))), // telegram
      creationFee, // creation_fee: Coin<SUI>
      tx.object('0x6'), // clock: &Clock
    ],
    // No type arguments needed for entry functions!
  });
  
  // Set gas budget
  tx.setGasBudget(100000000); // 0.1 SUI
  
  return tx;
}

/**
 * Creates a batch transaction for multiple token creation
 * More efficient for creating multiple tokens at once
 */
export async function createMultipleTokensTransaction(
  tokensData: CreateTokenData[],
  senderAddress: string
): Promise<Transaction> {
  const tx = new Transaction();
  
  // Validate batch size
  if (tokensData.length === 0 || tokensData.length > 10) {
    throw new Error('Batch size must be between 1 and 10 tokens');
  }
  
  // Set sender
  tx.setSender(senderAddress);
  
  // Calculate total fee needed
  const totalFee = DEFAULT_CREATION_FEE * BigInt(tokensData.length);
  const [creationFee] = tx.splitCoins(tx.gas, [totalFee]);
  
  // Prepare token creation parameters
  const tokenParams = tokensData.map(data => ({
    name: tx.pure.vector('u8', Array.from(new TextEncoder().encode(data.name))),
    symbol: tx.pure.vector('u8', Array.from(new TextEncoder().encode(data.symbol))),
    description: tx.pure.vector('u8', Array.from(new TextEncoder().encode(data.description || ''))),
    decimals: tx.pure.u8(data.decimals),
    total_supply: tx.pure.u64(data.totalSupply),
    image_url: tx.pure.vector('u8', Array.from(new TextEncoder().encode(data.imageUrl || ''))),
    twitter: tx.pure.vector('u8', Array.from(new TextEncoder().encode(data.twitter || ''))),
    website: tx.pure.vector('u8', Array.from(new TextEncoder().encode(data.website || ''))),
    telegram: tx.pure.vector('u8', Array.from(new TextEncoder().encode(data.telegram || ''))),
  }));
  
  // Call batch creation function
  tx.moveCall({
    target: `${PACKAGE_ID}::meme_coin_factory_v2::create_multiple_tokens`,
    arguments: [
      tx.object(PLATFORM_CONFIG),
      tx.object(TOKEN_REGISTRY),
      tx.pure(tokenParams), // vector of TokenCreationParams
      creationFee,
      tx.object('0x6'), // Clock
    ],
  });
  
  // Set higher gas budget for batch operations
  tx.setGasBudget(200000000); // 0.2 SUI
  
  return tx;
}

/**
 * Buy tokens from bonding curve
 */
export async function buyTokenTransaction(
  tokenId: string,
  suiAmount: bigint,
  minTokensOut: bigint,
  senderAddress: string
): Promise<Transaction> {
  const tx = new Transaction();
  
  tx.setSender(senderAddress);
  
  // Split SUI for purchase
  const [payment] = tx.splitCoins(tx.gas, [suiAmount]);
  
  tx.moveCall({
    target: `${PACKAGE_ID}::meme_coin_factory_v2::buy_token`,
    arguments: [
      tx.object(TOKEN_REGISTRY_V2),
      tx.object(BONDING_CURVE_STORAGE),
      tx.pure.id(tokenId),
      payment,
      tx.pure.u64(minTokensOut),
      tx.object('0x6'), // Clock
    ],
  });
  
  tx.setGasBudget(100000000);
  
  return tx;
}

/**
 * Sell tokens to bonding curve
 */
export async function sellTokenTransaction(
  tokenId: string,
  tokenAmount: bigint,
  minSuiOut: bigint,
  senderAddress: string
): Promise<Transaction> {
  const tx = new Transaction();
  
  tx.setSender(senderAddress);
  
  tx.moveCall({
    target: `${PACKAGE_ID}::meme_coin_factory_v2::sell_token`,
    arguments: [
      tx.object(TOKEN_REGISTRY_V2),
      tx.object(BONDING_CURVE_STORAGE),
      tx.pure.id(tokenId),
      tx.pure.u64(tokenAmount),
      tx.pure.u64(minSuiOut),
      tx.object('0x6'), // Clock
    ],
  });
  
  tx.setGasBudget(100000000);
  
  return tx;
}

/**
 * Enhanced validation with better error messages
 */
export function validateTokenData(data: CreateTokenData): { 
  valid: boolean; 
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  // Name validation
  if (!data.name || data.name.trim().length === 0) {
    errors.push('Token name is required');
  } else if (data.name.length > 32) {
    errors.push('Token name must be 32 characters or less');
  } else if (data.name.length < 3) {
    warnings.push('Token name is very short, consider using a longer name');
  }
  
  // Symbol validation
  if (!data.symbol || data.symbol.trim().length === 0) {
    errors.push('Token symbol is required');
  } else if (data.symbol.length > 10) {
    errors.push('Token symbol must be 10 characters or less');
  } else if (!/^[A-Z]+$/.test(data.symbol)) {
    errors.push('Token symbol must contain only uppercase letters');
  }
  
  // Description validation
  if (data.description && data.description.length > 500) {
    errors.push('Description must be 500 characters or less');
  }
  if (!data.description) {
    warnings.push('Consider adding a description to help users understand your token');
  }
  
  // Decimals validation
  if (data.decimals < 0 || data.decimals > 18) {
    errors.push('Decimals must be between 0 and 18');
  }
  if (data.decimals !== 9) {
    warnings.push('Most Sui tokens use 9 decimals, consider using 9 for compatibility');
  }
  
  // Total supply validation
  const totalSupply = parseInt(data.totalSupply);
  if (isNaN(totalSupply) || totalSupply < 1000) {
    errors.push('Total supply must be at least 1,000 tokens');
  } else if (totalSupply > 1000000000000000) {
    errors.push('Total supply cannot exceed 1,000,000,000,000,000');
  } else if (totalSupply > 1000000000000) {
    warnings.push('Very large supply may affect token value perception');
  }
  
  // URL validations
  const urlPattern = /^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/;
  
  if (data.imageUrl && !urlPattern.test(data.imageUrl)) {
    errors.push('Invalid image URL format');
  }
  if (!data.imageUrl) {
    warnings.push('Adding an image helps with token recognition and trust');
  }
  
  if (data.twitter && !urlPattern.test(data.twitter)) {
    errors.push('Invalid Twitter URL format');
  }
  
  if (data.website && !urlPattern.test(data.website)) {
    errors.push('Invalid website URL format');
  }
  
  if (data.telegram && !urlPattern.test(data.telegram)) {
    errors.push('Invalid Telegram URL format');
  }
  
  // Social media warnings
  if (!data.twitter && !data.telegram && !data.website) {
    warnings.push('Consider adding social media links to build community trust');
  }
  
  return {
    valid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * Retry logic for transaction execution
 */
export async function executeTransactionWithRetry(
  transaction: Transaction,
  signAndExecute: (tx: { transaction: Transaction }) => Promise<any>,
  maxRetries: number = 3
): Promise<any> {
  let lastError: Error | null = null;
  
  for (let i = 0; i < maxRetries; i++) {
    try {
      const result = await signAndExecute({ transaction });
      return result;
    } catch (error) {
      lastError = error as Error;
      
      // Don't retry on user rejection
      if (lastError.message.includes('User rejected') || 
          lastError.message.includes('User denied')) {
        throw lastError;
      }
      
      // Wait before retry with exponential backoff
      if (i < maxRetries - 1) {
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, i) * 1000));
      }
    }
  }
  
  throw lastError || new Error('Transaction failed after retries');
}

/**
 * Calculate slippage for token trades
 */
export function calculateSlippage(expectedAmount: bigint, slippageBps: number = 100): bigint {
  // Default 1% slippage (100 basis points)
  const slippageAmount = (expectedAmount * BigInt(slippageBps)) / BigInt(10000);
  return expectedAmount - slippageAmount;
}

/**
 * Format token amount with decimals
 */
export function formatTokenAmount(amount: string | number | bigint, decimals: number): string {
  const bigAmount = BigInt(amount);
  const divisor = BigInt(10 ** decimals);
  const whole = bigAmount / divisor;
  const remainder = bigAmount % divisor;
  
  if (remainder === BigInt(0)) {
    return whole.toString();
  }
  
  const remainderStr = remainder.toString().padStart(decimals, '0');
  const trimmed = remainderStr.replace(/0+$/, '');
  
  return `${whole}.${trimmed}`;
}