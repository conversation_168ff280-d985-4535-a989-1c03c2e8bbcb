{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Programming/sui/meme-coin-trading/frontend/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,mNAAgB,CAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,IAAA,yHAAE,EACX,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,mNAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,IAAA,yHAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,mNAAgB,CAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,IAAA,yHAAE,EACX,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,mNAAgB,CAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,IAAA,yHAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,mNAAgB,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,IAAA,yHAAE,EAAC,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,mNAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,IAAA,yHAAE,EAAC,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 89, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Programming/sui/meme-coin-trading/frontend/src/components/wallet/WalletGuard.tsx"], "sourcesContent": ["'use client';\n\nimport { useWallet } from '@suiet/wallet-kit';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { WalletConnectButton } from './WalletConnectButton';\nimport { Wallet } from 'lucide-react';\nimport { ReactNode } from 'react';\n\ninterface WalletGuardProps {\n  children: ReactNode;\n  message?: string;\n}\n\nexport function WalletGuard({ \n  children, \n  message = 'Please connect your wallet to continue'\n}: WalletGuardProps) {\n  const { connected } = useWallet();\n\n  if (!connected) {\n    return (\n      <div className=\"flex items-center justify-center min-h-[400px] p-4\">\n        <Card className=\"w-full max-w-md\">\n          <CardHeader className=\"text-center\">\n            <div className=\"flex justify-center mb-4\">\n              <Wallet className=\"h-12 w-12 text-muted-foreground\" />\n            </div>\n            <CardTitle>Connect Your Wallet</CardTitle>\n            <CardDescription>{message}</CardDescription>\n          </CardHeader>\n          <CardContent className=\"flex justify-center\">\n            <WalletConnectButton />\n          </CardContent>\n        </Card>\n      </div>\n    );\n  }\n\n  return <>{children}</>;\n}"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAaO,SAAS,YAAY,EAC1B,QAAQ,EACR,UAAU,wCAAwC,EACjC;IACjB,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,sKAAS;IAE/B,IAAI,CAAC,WAAW;QACd,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,wIAAI;gBAAC,WAAU;;kCACd,8OAAC,8IAAU;wBAAC,WAAU;;0CACpB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,gNAAM;oCAAC,WAAU;;;;;;;;;;;0CAEpB,8OAAC,6IAAS;0CAAC;;;;;;0CACX,8OAAC,mJAAe;0CAAE;;;;;;;;;;;;kCAEpB,8OAAC,+IAAW;wBAAC,WAAU;kCACrB,cAAA,8OAAC,0KAAmB;;;;;;;;;;;;;;;;;;;;;IAK9B;IAEA,qBAAO;kBAAG;;AACZ", "debugId": null}}, {"offset": {"line": 181, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Programming/sui/meme-coin-trading/frontend/src/lib/validations/token.ts"], "sourcesContent": ["import { z } from 'zod';\nimport {\n  MAX_NAME_LENGTH,\n  MAX_SYMBOL_LENGTH,\n  MAX_DESCRIPTION_LENGTH,\n  MAX_URL_LENGTH,\n  MAX_DECIMALS,\n  MIN_SUPPLY,\n  MAX_SUPPLY,\n  DEFAULT_DECIMALS,\n  DEFAULT_TOTAL_SUPPLY\n} from '@/lib/constants';\n\nconst urlSchema = z.string()\n  .max(MAX_URL_LENGTH, `URL must be ${MAX_URL_LENGTH} characters or less`)\n  .refine((url) => {\n    if (!url) return true; // Allow empty\n    try {\n      const parsed = new URL(url.startsWith('http') ? url : `https://${url}`);\n      return ['http:', 'https:'].includes(parsed.protocol);\n    } catch {\n      return false;\n    }\n  }, 'Invalid URL format')\n  .optional()\n  .or(z.literal(''));\n\nexport const createTokenSchema = z.object({\n  name: z.string()\n    .min(1, 'Token name is required')\n    .max(MAX_NAME_LENGTH, `Name must be ${MAX_NAME_LENGTH} characters or less`)\n    .trim(),\n  \n  symbol: z.string()\n    .min(1, 'Token symbol is required')\n    .max(MAX_SYMBOL_LENGTH, `Symbol must be ${MAX_SYMBOL_LENGTH} characters or less`)\n    .toUpperCase()\n    .regex(/^[A-Z]+$/, 'Symbol must contain only uppercase letters'),\n  \n  description: z.string()\n    .max(MAX_DESCRIPTION_LENGTH, `Description must be ${MAX_DESCRIPTION_LENGTH} characters or less`)\n    .optional()\n    .or(z.literal('')),\n  \n  decimals: z.coerce.number()\n    .int('Decimals must be an integer')\n    .min(0, 'Decimals must be at least 0')\n    .max(MAX_DECIMALS, `Decimals must be at most ${MAX_DECIMALS}`)\n    .default(DEFAULT_DECIMALS),\n  \n  totalSupply: z.coerce.number()\n    .int('Total supply must be an integer')\n    .min(MIN_SUPPLY, `Total supply must be at least ${MIN_SUPPLY.toLocaleString()}`)\n    .max(MAX_SUPPLY, `Total supply must be at most ${MAX_SUPPLY.toLocaleString()}`)\n    .default(DEFAULT_TOTAL_SUPPLY),\n  \n  imageUrl: urlSchema,\n  twitter: urlSchema,\n  website: urlSchema,\n  telegram: urlSchema,\n});\n\nexport type CreateTokenFormData = z.infer<typeof createTokenSchema>;"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAYA,MAAM,YAAY,kLAAC,CAAC,MAAM,GACvB,GAAG,CAAC,yIAAc,EAAE,CAAC,YAAY,EAAE,yIAAc,CAAC,mBAAmB,CAAC,EACtE,MAAM,CAAC,CAAC;IACP,IAAI,CAAC,KAAK,OAAO,MAAM,cAAc;IACrC,IAAI;QACF,MAAM,SAAS,IAAI,IAAI,IAAI,UAAU,CAAC,UAAU,MAAM,CAAC,QAAQ,EAAE,KAAK;QACtE,OAAO;YAAC;YAAS;SAAS,CAAC,QAAQ,CAAC,OAAO,QAAQ;IACrD,EAAE,OAAM;QACN,OAAO;IACT;AACF,GAAG,sBACF,QAAQ,GACR,EAAE,CAAC,kLAAC,CAAC,OAAO,CAAC;AAET,MAAM,oBAAoB,kLAAC,CAAC,MAAM,CAAC;IACxC,MAAM,kLAAC,CAAC,MAAM,GACX,GAAG,CAAC,GAAG,0BACP,GAAG,CAAC,0IAAe,EAAE,CAAC,aAAa,EAAE,0IAAe,CAAC,mBAAmB,CAAC,EACzE,IAAI;IAEP,QAAQ,kLAAC,CAAC,MAAM,GACb,GAAG,CAAC,GAAG,4BACP,GAAG,CAAC,4IAAiB,EAAE,CAAC,eAAe,EAAE,4IAAiB,CAAC,mBAAmB,CAAC,EAC/E,WAAW,GACX,KAAK,CAAC,YAAY;IAErB,aAAa,kLAAC,CAAC,MAAM,GAClB,GAAG,CAAC,iJAAsB,EAAE,CAAC,oBAAoB,EAAE,iJAAsB,CAAC,mBAAmB,CAAC,EAC9F,QAAQ,GACR,EAAE,CAAC,kLAAC,CAAC,OAAO,CAAC;IAEhB,UAAU,kLAAC,CAAC,MAAM,CAAC,MAAM,GACtB,GAAG,CAAC,+BACJ,GAAG,CAAC,GAAG,+BACP,GAAG,CAAC,uIAAY,EAAE,CAAC,yBAAyB,EAAE,uIAAY,EAAE,EAC5D,OAAO,CAAC,2IAAgB;IAE3B,aAAa,kLAAC,CAAC,MAAM,CAAC,MAAM,GACzB,GAAG,CAAC,mCACJ,GAAG,CAAC,qIAAU,EAAE,CAAC,8BAA8B,EAAE,qIAAU,CAAC,cAAc,IAAI,EAC9E,GAAG,CAAC,qIAAU,EAAE,CAAC,6BAA6B,EAAE,qIAAU,CAAC,cAAc,IAAI,EAC7E,OAAO,CAAC,+IAAoB;IAE/B,UAAU;IACV,SAAS;IACT,SAAS;IACT,UAAU;AACZ", "debugId": null}}, {"offset": {"line": 216, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Programming/sui/meme-coin-trading/frontend/src/lib/sui/transactions-v2.ts"], "sourcesContent": ["import { Transaction } from '@mysten/sui/transactions';\nimport { \n  PACKAGE_ID, \n  PLATFORM_CONFIG_V2,\n  TOKEN_REGISTRY_V2,\n  BONDING_CURVE_STORAGE,\n  DEFAULT_CREATION_FEE \n} from '@/lib/constants';\nimport { CreateTokenData } from '@/types/token';\n\n/**\n * Creates a transaction for minting a new meme token using the V2 entry function\n * This function can be called directly from the frontend without witness requirements\n */\nexport async function createMemeTokenTransaction(\n  tokenData: CreateTokenData,\n  senderAddress: string\n): Promise<Transaction> {\n  const tx = new Transaction();\n  \n  // Set sender\n  tx.setSender(senderAddress);\n  \n  // Split coins for creation fee (0.1 SUI)\n  const [creationFee] = tx.splitCoins(tx.gas, [DEFAULT_CREATION_FEE]);\n  \n  // Call the new entry function directly - no witness needed!\n  tx.moveCall({\n    target: `${PACKAGE_ID}::meme_coin_factory_v2::create_meme_token_entry`,\n    arguments: [\n      tx.object(PLATFORM_CONFIG_V2), // config: &mut PlatformConfigV2\n      tx.object(TOKEN_REGISTRY_V2), // registry: &mut TokenRegistryV2\n      tx.object(BONDING_CURVE_STORAGE), // storage: &mut BondingCurveStorage\n      tx.pure.vector('u8', Array.from(new TextEncoder().encode(tokenData.name))), // name\n      tx.pure.vector('u8', Array.from(new TextEncoder().encode(tokenData.symbol))), // symbol\n      tx.pure.vector('u8', Array.from(new TextEncoder().encode(tokenData.description || ''))), // description\n      tx.pure.u8(tokenData.decimals), // decimals\n      tx.pure.u64(tokenData.totalSupply), // total_supply\n      tx.pure.vector('u8', Array.from(new TextEncoder().encode(tokenData.imageUrl || ''))), // image_url\n      tx.pure.vector('u8', Array.from(new TextEncoder().encode(tokenData.twitter || ''))), // twitter\n      tx.pure.vector('u8', Array.from(new TextEncoder().encode(tokenData.website || ''))), // website\n      tx.pure.vector('u8', Array.from(new TextEncoder().encode(tokenData.telegram || ''))), // telegram\n      creationFee, // creation_fee: Coin<SUI>\n      tx.object('0x6'), // clock: &Clock\n    ],\n    // No type arguments needed for entry functions!\n  });\n  \n  // Set gas budget\n  tx.setGasBudget(100000000); // 0.1 SUI\n  \n  return tx;\n}\n\n/**\n * Creates a batch transaction for multiple token creation\n * More efficient for creating multiple tokens at once\n */\nexport async function createMultipleTokensTransaction(\n  tokensData: CreateTokenData[],\n  senderAddress: string\n): Promise<Transaction> {\n  const tx = new Transaction();\n  \n  // Validate batch size\n  if (tokensData.length === 0 || tokensData.length > 10) {\n    throw new Error('Batch size must be between 1 and 10 tokens');\n  }\n  \n  // Set sender\n  tx.setSender(senderAddress);\n  \n  // Calculate total fee needed\n  const totalFee = DEFAULT_CREATION_FEE * BigInt(tokensData.length);\n  const [creationFee] = tx.splitCoins(tx.gas, [totalFee]);\n  \n  // Prepare token creation parameters\n  const tokenParams = tokensData.map(data => ({\n    name: tx.pure.vector('u8', Array.from(new TextEncoder().encode(data.name))),\n    symbol: tx.pure.vector('u8', Array.from(new TextEncoder().encode(data.symbol))),\n    description: tx.pure.vector('u8', Array.from(new TextEncoder().encode(data.description || ''))),\n    decimals: tx.pure.u8(data.decimals),\n    total_supply: tx.pure.u64(data.totalSupply),\n    image_url: tx.pure.vector('u8', Array.from(new TextEncoder().encode(data.imageUrl || ''))),\n    twitter: tx.pure.vector('u8', Array.from(new TextEncoder().encode(data.twitter || ''))),\n    website: tx.pure.vector('u8', Array.from(new TextEncoder().encode(data.website || ''))),\n    telegram: tx.pure.vector('u8', Array.from(new TextEncoder().encode(data.telegram || ''))),\n  }));\n  \n  // Call batch creation function\n  tx.moveCall({\n    target: `${PACKAGE_ID}::meme_coin_factory_v2::create_multiple_tokens`,\n    arguments: [\n      tx.object(PLATFORM_CONFIG),\n      tx.object(TOKEN_REGISTRY),\n      tx.pure(tokenParams), // vector of TokenCreationParams\n      creationFee,\n      tx.object('0x6'), // Clock\n    ],\n  });\n  \n  // Set higher gas budget for batch operations\n  tx.setGasBudget(200000000); // 0.2 SUI\n  \n  return tx;\n}\n\n/**\n * Buy tokens from bonding curve\n */\nexport async function buyTokenTransaction(\n  tokenId: string,\n  suiAmount: bigint,\n  minTokensOut: bigint,\n  senderAddress: string\n): Promise<Transaction> {\n  const tx = new Transaction();\n  \n  tx.setSender(senderAddress);\n  \n  // Split SUI for purchase\n  const [payment] = tx.splitCoins(tx.gas, [suiAmount]);\n  \n  tx.moveCall({\n    target: `${PACKAGE_ID}::meme_coin_factory_v2::buy_token`,\n    arguments: [\n      tx.object(TOKEN_REGISTRY_V2),\n      tx.object(BONDING_CURVE_STORAGE),\n      tx.pure.id(tokenId),\n      payment,\n      tx.pure.u64(minTokensOut),\n      tx.object('0x6'), // Clock\n    ],\n  });\n  \n  tx.setGasBudget(100000000);\n  \n  return tx;\n}\n\n/**\n * Sell tokens to bonding curve\n */\nexport async function sellTokenTransaction(\n  tokenId: string,\n  tokenAmount: bigint,\n  minSuiOut: bigint,\n  senderAddress: string\n): Promise<Transaction> {\n  const tx = new Transaction();\n  \n  tx.setSender(senderAddress);\n  \n  tx.moveCall({\n    target: `${PACKAGE_ID}::meme_coin_factory_v2::sell_token`,\n    arguments: [\n      tx.object(TOKEN_REGISTRY_V2),\n      tx.object(BONDING_CURVE_STORAGE),\n      tx.pure.id(tokenId),\n      tx.pure.u64(tokenAmount),\n      tx.pure.u64(minSuiOut),\n      tx.object('0x6'), // Clock\n    ],\n  });\n  \n  tx.setGasBudget(100000000);\n  \n  return tx;\n}\n\n/**\n * Enhanced validation with better error messages\n */\nexport function validateTokenData(data: CreateTokenData): { \n  valid: boolean; \n  errors: string[];\n  warnings: string[];\n} {\n  const errors: string[] = [];\n  const warnings: string[] = [];\n  \n  // Name validation\n  if (!data.name || data.name.trim().length === 0) {\n    errors.push('Token name is required');\n  } else if (data.name.length > 32) {\n    errors.push('Token name must be 32 characters or less');\n  } else if (data.name.length < 3) {\n    warnings.push('Token name is very short, consider using a longer name');\n  }\n  \n  // Symbol validation\n  if (!data.symbol || data.symbol.trim().length === 0) {\n    errors.push('Token symbol is required');\n  } else if (data.symbol.length > 10) {\n    errors.push('Token symbol must be 10 characters or less');\n  } else if (!/^[A-Z]+$/.test(data.symbol)) {\n    errors.push('Token symbol must contain only uppercase letters');\n  }\n  \n  // Description validation\n  if (data.description && data.description.length > 500) {\n    errors.push('Description must be 500 characters or less');\n  }\n  if (!data.description) {\n    warnings.push('Consider adding a description to help users understand your token');\n  }\n  \n  // Decimals validation\n  if (data.decimals < 0 || data.decimals > 18) {\n    errors.push('Decimals must be between 0 and 18');\n  }\n  if (data.decimals !== 9) {\n    warnings.push('Most Sui tokens use 9 decimals, consider using 9 for compatibility');\n  }\n  \n  // Total supply validation\n  const totalSupply = parseInt(data.totalSupply);\n  if (isNaN(totalSupply) || totalSupply < 1000) {\n    errors.push('Total supply must be at least 1,000 tokens');\n  } else if (totalSupply > 1000000000000000) {\n    errors.push('Total supply cannot exceed 1,000,000,000,000,000');\n  } else if (totalSupply > 1000000000000) {\n    warnings.push('Very large supply may affect token value perception');\n  }\n  \n  // URL validations\n  const urlPattern = /^(https?:\\/\\/)?([\\da-z\\.-]+)\\.([a-z\\.]{2,6})([\\/\\w \\.-]*)*\\/?$/;\n  \n  if (data.imageUrl && !urlPattern.test(data.imageUrl)) {\n    errors.push('Invalid image URL format');\n  }\n  if (!data.imageUrl) {\n    warnings.push('Adding an image helps with token recognition and trust');\n  }\n  \n  if (data.twitter && !urlPattern.test(data.twitter)) {\n    errors.push('Invalid Twitter URL format');\n  }\n  \n  if (data.website && !urlPattern.test(data.website)) {\n    errors.push('Invalid website URL format');\n  }\n  \n  if (data.telegram && !urlPattern.test(data.telegram)) {\n    errors.push('Invalid Telegram URL format');\n  }\n  \n  // Social media warnings\n  if (!data.twitter && !data.telegram && !data.website) {\n    warnings.push('Consider adding social media links to build community trust');\n  }\n  \n  return {\n    valid: errors.length === 0,\n    errors,\n    warnings\n  };\n}\n\n/**\n * Retry logic for transaction execution\n */\nexport async function executeTransactionWithRetry(\n  transaction: Transaction,\n  signAndExecute: (tx: { transaction: Transaction }) => Promise<any>,\n  maxRetries: number = 3\n): Promise<any> {\n  let lastError: Error | null = null;\n  \n  for (let i = 0; i < maxRetries; i++) {\n    try {\n      const result = await signAndExecute({ transaction });\n      return result;\n    } catch (error) {\n      lastError = error as Error;\n      \n      // Don't retry on user rejection\n      if (lastError.message.includes('User rejected') || \n          lastError.message.includes('User denied')) {\n        throw lastError;\n      }\n      \n      // Wait before retry with exponential backoff\n      if (i < maxRetries - 1) {\n        await new Promise(resolve => setTimeout(resolve, Math.pow(2, i) * 1000));\n      }\n    }\n  }\n  \n  throw lastError || new Error('Transaction failed after retries');\n}\n\n/**\n * Calculate slippage for token trades\n */\nexport function calculateSlippage(expectedAmount: bigint, slippageBps: number = 100): bigint {\n  // Default 1% slippage (100 basis points)\n  const slippageAmount = (expectedAmount * BigInt(slippageBps)) / BigInt(10000);\n  return expectedAmount - slippageAmount;\n}\n\n/**\n * Format token amount with decimals\n */\nexport function formatTokenAmount(amount: string | number | bigint, decimals: number): string {\n  const bigAmount = BigInt(amount);\n  const divisor = BigInt(10 ** decimals);\n  const whole = bigAmount / divisor;\n  const remainder = bigAmount % divisor;\n  \n  if (remainder === BigInt(0)) {\n    return whole.toString();\n  }\n  \n  const remainderStr = remainder.toString().padStart(decimals, '0');\n  const trimmed = remainderStr.replace(/0+$/, '');\n  \n  return `${whole}.${trimmed}`;\n}"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AACA;;;AAaO,eAAe,2BACpB,SAA0B,EAC1B,aAAqB;IAErB,MAAM,KAAK,IAAI,4LAAW;IAE1B,aAAa;IACb,GAAG,SAAS,CAAC;IAEb,yCAAyC;IACzC,MAAM,CAAC,YAAY,GAAG,GAAG,UAAU,CAAC,GAAG,GAAG,EAAE;QAAC,+IAAoB;KAAC;IAElE,4DAA4D;IAC5D,GAAG,QAAQ,CAAC;QACV,QAAQ,GAAG,qIAAU,CAAC,+CAA+C,CAAC;QACtE,WAAW;YACT,GAAG,MAAM,CAAC,6IAAkB;YAC5B,GAAG,MAAM,CAAC,4IAAiB;YAC3B,GAAG,MAAM,CAAC,gJAAqB;YAC/B,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,MAAM,IAAI,CAAC,IAAI,cAAc,MAAM,CAAC,UAAU,IAAI;YACvE,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,MAAM,IAAI,CAAC,IAAI,cAAc,MAAM,CAAC,UAAU,MAAM;YACzE,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,MAAM,IAAI,CAAC,IAAI,cAAc,MAAM,CAAC,UAAU,WAAW,IAAI;YAClF,GAAG,IAAI,CAAC,EAAE,CAAC,UAAU,QAAQ;YAC7B,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,WAAW;YACjC,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,MAAM,IAAI,CAAC,IAAI,cAAc,MAAM,CAAC,UAAU,QAAQ,IAAI;YAC/E,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,MAAM,IAAI,CAAC,IAAI,cAAc,MAAM,CAAC,UAAU,OAAO,IAAI;YAC9E,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,MAAM,IAAI,CAAC,IAAI,cAAc,MAAM,CAAC,UAAU,OAAO,IAAI;YAC9E,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,MAAM,IAAI,CAAC,IAAI,cAAc,MAAM,CAAC,UAAU,QAAQ,IAAI;YAC/E;YACA,GAAG,MAAM,CAAC;SACX;IAEH;IAEA,iBAAiB;IACjB,GAAG,YAAY,CAAC,YAAY,UAAU;IAEtC,OAAO;AACT;AAMO,eAAe,gCACpB,UAA6B,EAC7B,aAAqB;IAErB,MAAM,KAAK,IAAI,4LAAW;IAE1B,sBAAsB;IACtB,IAAI,WAAW,MAAM,KAAK,KAAK,WAAW,MAAM,GAAG,IAAI;QACrD,MAAM,IAAI,MAAM;IAClB;IAEA,aAAa;IACb,GAAG,SAAS,CAAC;IAEb,6BAA6B;IAC7B,MAAM,WAAW,+IAAoB,GAAG,OAAO,WAAW,MAAM;IAChE,MAAM,CAAC,YAAY,GAAG,GAAG,UAAU,CAAC,GAAG,GAAG,EAAE;QAAC;KAAS;IAEtD,oCAAoC;IACpC,MAAM,cAAc,WAAW,GAAG,CAAC,CAAA,OAAQ,CAAC;YAC1C,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,MAAM,IAAI,CAAC,IAAI,cAAc,MAAM,CAAC,KAAK,IAAI;YACxE,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,MAAM,IAAI,CAAC,IAAI,cAAc,MAAM,CAAC,KAAK,MAAM;YAC5E,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,MAAM,IAAI,CAAC,IAAI,cAAc,MAAM,CAAC,KAAK,WAAW,IAAI;YAC1F,UAAU,GAAG,IAAI,CAAC,EAAE,CAAC,KAAK,QAAQ;YAClC,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,WAAW;YAC1C,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,MAAM,IAAI,CAAC,IAAI,cAAc,MAAM,CAAC,KAAK,QAAQ,IAAI;YACrF,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,MAAM,IAAI,CAAC,IAAI,cAAc,MAAM,CAAC,KAAK,OAAO,IAAI;YAClF,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,MAAM,IAAI,CAAC,IAAI,cAAc,MAAM,CAAC,KAAK,OAAO,IAAI;YAClF,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,MAAM,IAAI,CAAC,IAAI,cAAc,MAAM,CAAC,KAAK,QAAQ,IAAI;QACtF,CAAC;IAED,+BAA+B;IAC/B,GAAG,QAAQ,CAAC;QACV,QAAQ,GAAG,qIAAU,CAAC,8CAA8C,CAAC;QACrE,WAAW;YACT,GAAG,MAAM,CAAC;YACV,GAAG,MAAM,CAAC;YACV,GAAG,IAAI,CAAC;YACR;YACA,GAAG,MAAM,CAAC;SACX;IACH;IAEA,6CAA6C;IAC7C,GAAG,YAAY,CAAC,YAAY,UAAU;IAEtC,OAAO;AACT;AAKO,eAAe,oBACpB,OAAe,EACf,SAAiB,EACjB,YAAoB,EACpB,aAAqB;IAErB,MAAM,KAAK,IAAI,4LAAW;IAE1B,GAAG,SAAS,CAAC;IAEb,yBAAyB;IACzB,MAAM,CAAC,QAAQ,GAAG,GAAG,UAAU,CAAC,GAAG,GAAG,EAAE;QAAC;KAAU;IAEnD,GAAG,QAAQ,CAAC;QACV,QAAQ,GAAG,qIAAU,CAAC,iCAAiC,CAAC;QACxD,WAAW;YACT,GAAG,MAAM,CAAC,4IAAiB;YAC3B,GAAG,MAAM,CAAC,gJAAqB;YAC/B,GAAG,IAAI,CAAC,EAAE,CAAC;YACX;YACA,GAAG,IAAI,CAAC,GAAG,CAAC;YACZ,GAAG,MAAM,CAAC;SACX;IACH;IAEA,GAAG,YAAY,CAAC;IAEhB,OAAO;AACT;AAKO,eAAe,qBACpB,OAAe,EACf,WAAmB,EACnB,SAAiB,EACjB,aAAqB;IAErB,MAAM,KAAK,IAAI,4LAAW;IAE1B,GAAG,SAAS,CAAC;IAEb,GAAG,QAAQ,CAAC;QACV,QAAQ,GAAG,qIAAU,CAAC,kCAAkC,CAAC;QACzD,WAAW;YACT,GAAG,MAAM,CAAC,4IAAiB;YAC3B,GAAG,MAAM,CAAC,gJAAqB;YAC/B,GAAG,IAAI,CAAC,EAAE,CAAC;YACX,GAAG,IAAI,CAAC,GAAG,CAAC;YACZ,GAAG,IAAI,CAAC,GAAG,CAAC;YACZ,GAAG,MAAM,CAAC;SACX;IACH;IAEA,GAAG,YAAY,CAAC;IAEhB,OAAO;AACT;AAKO,SAAS,kBAAkB,IAAqB;IAKrD,MAAM,SAAmB,EAAE;IAC3B,MAAM,WAAqB,EAAE;IAE7B,kBAAkB;IAClB,IAAI,CAAC,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,IAAI,GAAG,MAAM,KAAK,GAAG;QAC/C,OAAO,IAAI,CAAC;IACd,OAAO,IAAI,KAAK,IAAI,CAAC,MAAM,GAAG,IAAI;QAChC,OAAO,IAAI,CAAC;IACd,OAAO,IAAI,KAAK,IAAI,CAAC,MAAM,GAAG,GAAG;QAC/B,SAAS,IAAI,CAAC;IAChB;IAEA,oBAAoB;IACpB,IAAI,CAAC,KAAK,MAAM,IAAI,KAAK,MAAM,CAAC,IAAI,GAAG,MAAM,KAAK,GAAG;QACnD,OAAO,IAAI,CAAC;IACd,OAAO,IAAI,KAAK,MAAM,CAAC,MAAM,GAAG,IAAI;QAClC,OAAO,IAAI,CAAC;IACd,OAAO,IAAI,CAAC,WAAW,IAAI,CAAC,KAAK,MAAM,GAAG;QACxC,OAAO,IAAI,CAAC;IACd;IAEA,yBAAyB;IACzB,IAAI,KAAK,WAAW,IAAI,KAAK,WAAW,CAAC,MAAM,GAAG,KAAK;QACrD,OAAO,IAAI,CAAC;IACd;IACA,IAAI,CAAC,KAAK,WAAW,EAAE;QACrB,SAAS,IAAI,CAAC;IAChB;IAEA,sBAAsB;IACtB,IAAI,KAAK,QAAQ,GAAG,KAAK,KAAK,QAAQ,GAAG,IAAI;QAC3C,OAAO,IAAI,CAAC;IACd;IACA,IAAI,KAAK,QAAQ,KAAK,GAAG;QACvB,SAAS,IAAI,CAAC;IAChB;IAEA,0BAA0B;IAC1B,MAAM,cAAc,SAAS,KAAK,WAAW;IAC7C,IAAI,MAAM,gBAAgB,cAAc,MAAM;QAC5C,OAAO,IAAI,CAAC;IACd,OAAO,IAAI,cAAc,kBAAkB;QACzC,OAAO,IAAI,CAAC;IACd,OAAO,IAAI,cAAc,eAAe;QACtC,SAAS,IAAI,CAAC;IAChB;IAEA,kBAAkB;IAClB,MAAM,aAAa;IAEnB,IAAI,KAAK,QAAQ,IAAI,CAAC,WAAW,IAAI,CAAC,KAAK,QAAQ,GAAG;QACpD,OAAO,IAAI,CAAC;IACd;IACA,IAAI,CAAC,KAAK,QAAQ,EAAE;QAClB,SAAS,IAAI,CAAC;IAChB;IAEA,IAAI,KAAK,OAAO,IAAI,CAAC,WAAW,IAAI,CAAC,KAAK,OAAO,GAAG;QAClD,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,KAAK,OAAO,IAAI,CAAC,WAAW,IAAI,CAAC,KAAK,OAAO,GAAG;QAClD,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,KAAK,QAAQ,IAAI,CAAC,WAAW,IAAI,CAAC,KAAK,QAAQ,GAAG;QACpD,OAAO,IAAI,CAAC;IACd;IAEA,wBAAwB;IACxB,IAAI,CAAC,KAAK,OAAO,IAAI,CAAC,KAAK,QAAQ,IAAI,CAAC,KAAK,OAAO,EAAE;QACpD,SAAS,IAAI,CAAC;IAChB;IAEA,OAAO;QACL,OAAO,OAAO,MAAM,KAAK;QACzB;QACA;IACF;AACF;AAKO,eAAe,4BACpB,WAAwB,EACxB,cAAkE,EAClE,aAAqB,CAAC;IAEtB,IAAI,YAA0B;IAE9B,IAAK,IAAI,IAAI,GAAG,IAAI,YAAY,IAAK;QACnC,IAAI;YACF,MAAM,SAAS,MAAM,eAAe;gBAAE;YAAY;YAClD,OAAO;QACT,EAAE,OAAO,OAAO;YACd,YAAY;YAEZ,gCAAgC;YAChC,IAAI,UAAU,OAAO,CAAC,QAAQ,CAAC,oBAC3B,UAAU,OAAO,CAAC,QAAQ,CAAC,gBAAgB;gBAC7C,MAAM;YACR;YAEA,6CAA6C;YAC7C,IAAI,IAAI,aAAa,GAAG;gBACtB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS,KAAK,GAAG,CAAC,GAAG,KAAK;YACpE;QACF;IACF;IAEA,MAAM,aAAa,IAAI,MAAM;AAC/B;AAKO,SAAS,kBAAkB,cAAsB,EAAE,cAAsB,GAAG;IACjF,yCAAyC;IACzC,MAAM,iBAAiB,AAAC,iBAAiB,OAAO,eAAgB,OAAO;IACvE,OAAO,iBAAiB;AAC1B;AAKO,SAAS,kBAAkB,MAAgC,EAAE,QAAgB;IAClF,MAAM,YAAY,OAAO;IACzB,MAAM,UAAU,OAAO,MAAM;IAC7B,MAAM,QAAQ,YAAY;IAC1B,MAAM,YAAY,YAAY;IAE9B,IAAI,cAAc,OAAO,IAAI;QAC3B,OAAO,MAAM,QAAQ;IACvB;IAEA,MAAM,eAAe,UAAU,QAAQ,GAAG,QAAQ,CAAC,UAAU;IAC7D,MAAM,UAAU,aAAa,OAAO,CAAC,OAAO;IAE5C,OAAO,GAAG,MAAM,CAAC,EAAE,SAAS;AAC9B", "debugId": null}}, {"offset": {"line": 460, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Programming/sui/meme-coin-trading/frontend/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst labelVariants = cva(\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\n)\n\nconst Label = React.forwardRef<\n  React.ElementRef<typeof LabelPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\n    VariantProps<typeof labelVariants>\n>(({ className, ...props }, ref) => (\n  <LabelPrimitive.Root\n    ref={ref}\n    className={cn(labelVariants(), className)}\n    {...props}\n  />\n))\nLabel.displayName = LabelPrimitive.Root.displayName\n\nexport { Label }\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,gBAAgB,IAAA,uKAAG,EACvB;AAGF,MAAM,sBAAQ,mNAAgB,CAI5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,yKAAmB;QAClB,KAAK;QACL,WAAW,IAAA,yHAAE,EAAC,iBAAiB;QAC9B,GAAG,KAAK;;;;;;AAGb,MAAM,WAAW,GAAG,yKAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 491, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Programming/sui/meme-coin-trading/frontend/src/components/ui/form.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport {\n  Controller,\n  FormProvider,\n  useFormContext,\n  type ControllerProps,\n  type FieldPath,\n  type FieldValues,\n} from \"react-hook-form\"\n\nimport { cn } from \"@/lib/utils\"\nimport { Label } from \"@/components/ui/label\"\n\nconst Form = FormProvider\n\ntype FormFieldContextValue<\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>\n> = {\n  name: TName\n}\n\nconst FormFieldContext = React.createContext<FormFieldContextValue>(\n  {} as FormFieldContextValue\n)\n\nconst FormField = <\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>\n>({\n  ...props\n}: ControllerProps<TFieldValues, TName>) => {\n  return (\n    <FormFieldContext.Provider value={{ name: props.name }}>\n      <Controller {...props} />\n    </FormFieldContext.Provider>\n  )\n}\n\nconst useFormField = () => {\n  const fieldContext = React.useContext(FormFieldContext)\n  const itemContext = React.useContext(FormItemContext)\n  const { getFieldState, formState } = useFormContext()\n\n  const fieldState = getFieldState(fieldContext.name, formState)\n\n  if (!fieldContext) {\n    throw new Error(\"useFormField should be used within <FormField>\")\n  }\n\n  const { id } = itemContext\n\n  return {\n    id,\n    name: fieldContext.name,\n    formItemId: `${id}-form-item`,\n    formDescriptionId: `${id}-form-item-description`,\n    formMessageId: `${id}-form-item-message`,\n    ...fieldState,\n  }\n}\n\ntype FormItemContextValue = {\n  id: string\n}\n\nconst FormItemContext = React.createContext<FormItemContextValue>(\n  {} as FormItemContextValue\n)\n\nconst FormItem = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => {\n  const id = React.useId()\n\n  return (\n    <FormItemContext.Provider value={{ id }}>\n      <div ref={ref} className={cn(\"space-y-2\", className)} {...props} />\n    </FormItemContext.Provider>\n  )\n})\nFormItem.displayName = \"FormItem\"\n\nconst FormLabel = React.forwardRef<\n  React.ElementRef<typeof LabelPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root>\n>(({ className, ...props }, ref) => {\n  const { error, formItemId } = useFormField()\n\n  return (\n    <Label\n      ref={ref}\n      className={cn(error && \"text-destructive\", className)}\n      htmlFor={formItemId}\n      {...props}\n    />\n  )\n})\nFormLabel.displayName = \"FormLabel\"\n\nconst FormControl = React.forwardRef<\n  React.ElementRef<typeof Slot>,\n  React.ComponentPropsWithoutRef<typeof Slot>\n>(({ ...props }, ref) => {\n  const { error, formItemId, formDescriptionId, formMessageId } = useFormField()\n\n  return (\n    <Slot\n      ref={ref}\n      id={formItemId}\n      aria-describedby={\n        !error\n          ? `${formDescriptionId}`\n          : `${formDescriptionId} ${formMessageId}`\n      }\n      aria-invalid={!!error}\n      {...props}\n    />\n  )\n})\nFormControl.displayName = \"FormControl\"\n\nconst FormDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => {\n  const { formDescriptionId } = useFormField()\n\n  return (\n    <p\n      ref={ref}\n      id={formDescriptionId}\n      className={cn(\"text-sm text-muted-foreground\", className)}\n      {...props}\n    />\n  )\n})\nFormDescription.displayName = \"FormDescription\"\n\nconst FormMessage = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, children, ...props }, ref) => {\n  const { error, formMessageId } = useFormField()\n  const body = error ? String(error?.message ?? \"\") : children\n\n  if (!body) {\n    return null\n  }\n\n  return (\n    <p\n      ref={ref}\n      id={formMessageId}\n      className={cn(\"text-sm font-medium text-destructive\", className)}\n      {...props}\n    >\n      {body}\n    </p>\n  )\n})\nFormMessage.displayName = \"FormMessage\"\n\nexport {\n  useFormField,\n  Form,\n  FormItem,\n  FormLabel,\n  FormControl,\n  FormDescription,\n  FormMessage,\n  FormField,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AAEA;AAEA;AACA;AASA;AACA;AAfA;;;;;;;AAiBA,MAAM,OAAO,8KAAY;AASzB,MAAM,iCAAmB,sNAAmB,CAC1C,CAAC;AAGH,MAAM,YAAY,CAGhB,EACA,GAAG,OACkC;IACrC,qBACE,8OAAC,iBAAiB,QAAQ;QAAC,OAAO;YAAE,MAAM,MAAM,IAAI;QAAC;kBACnD,cAAA,8OAAC,4KAAU;YAAE,GAAG,KAAK;;;;;;;;;;;AAG3B;AAEA,MAAM,eAAe;IACnB,MAAM,eAAe,mNAAgB,CAAC;IACtC,MAAM,cAAc,mNAAgB,CAAC;IACrC,MAAM,EAAE,aAAa,EAAE,SAAS,EAAE,GAAG,IAAA,gLAAc;IAEnD,MAAM,aAAa,cAAc,aAAa,IAAI,EAAE;IAEpD,IAAI,CAAC,cAAc;QACjB,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,EAAE,EAAE,EAAE,GAAG;IAEf,OAAO;QACL;QACA,MAAM,aAAa,IAAI;QACvB,YAAY,GAAG,GAAG,UAAU,CAAC;QAC7B,mBAAmB,GAAG,GAAG,sBAAsB,CAAC;QAChD,eAAe,GAAG,GAAG,kBAAkB,CAAC;QACxC,GAAG,UAAU;IACf;AACF;AAMA,MAAM,gCAAkB,sNAAmB,CACzC,CAAC;AAGH,MAAM,yBAAW,mNAAgB,CAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,MAAM,KAAK,8MAAW;IAEtB,qBACE,8OAAC,gBAAgB,QAAQ;QAAC,OAAO;YAAE;QAAG;kBACpC,cAAA,8OAAC;YAAI,KAAK;YAAK,WAAW,IAAA,yHAAE,EAAC,aAAa;YAAa,GAAG,KAAK;;;;;;;;;;;AAGrE;AACA,SAAS,WAAW,GAAG;AAEvB,MAAM,0BAAY,mNAAgB,CAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG;IAE9B,qBACE,8OAAC,0IAAK;QACJ,KAAK;QACL,WAAW,IAAA,yHAAE,EAAC,SAAS,oBAAoB;QAC3C,SAAS;QACR,GAAG,KAAK;;;;;;AAGf;AACA,UAAU,WAAW,GAAG;AAExB,MAAM,4BAAc,mNAAgB,CAGlC,CAAC,EAAE,GAAG,OAAO,EAAE;IACf,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,iBAAiB,EAAE,aAAa,EAAE,GAAG;IAEhE,qBACE,8OAAC,wKAAI;QACH,KAAK;QACL,IAAI;QACJ,oBACE,CAAC,QACG,GAAG,mBAAmB,GACtB,GAAG,kBAAkB,CAAC,EAAE,eAAe;QAE7C,gBAAc,CAAC,CAAC;QACf,GAAG,KAAK;;;;;;AAGf;AACA,YAAY,WAAW,GAAG;AAE1B,MAAM,gCAAkB,mNAAgB,CAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,MAAM,EAAE,iBAAiB,EAAE,GAAG;IAE9B,qBACE,8OAAC;QACC,KAAK;QACL,IAAI;QACJ,WAAW,IAAA,yHAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AACA,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,mNAAgB,CAGlC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACpC,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG;IACjC,MAAM,OAAO,QAAQ,OAAO,OAAO,WAAW,MAAM;IAEpD,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,8OAAC;QACC,KAAK;QACL,IAAI;QACJ,WAAW,IAAA,yHAAE,EAAC,wCAAwC;QACrD,GAAG,KAAK;kBAER;;;;;;AAGP;AACA,YAAY,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 650, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Programming/sui/meme-coin-trading/frontend/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Input = React.forwardRef<HTMLInputElement, React.ComponentProps<\"input\">>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,mNAAgB,CAC5B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,8OAAC;QACC,MAAM;QACN,WAAW,IAAA,yHAAE,EACX,kYACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 678, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Programming/sui/meme-coin-trading/frontend/src/components/ui/alert.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst alertVariants = cva(\n  \"relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-background text-foreground\",\n        destructive:\n          \"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nconst Alert = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement> & VariantProps<typeof alertVariants>\n>(({ className, variant, ...props }, ref) => (\n  <div\n    ref={ref}\n    role=\"alert\"\n    className={cn(alertVariants({ variant }), className)}\n    {...props}\n  />\n))\nAlert.displayName = \"Alert\"\n\nconst AlertTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h5\n    ref={ref}\n    className={cn(\"mb-1 font-medium leading-none tracking-tight\", className)}\n    {...props}\n  />\n))\nAlertTitle.displayName = \"AlertTitle\"\n\nconst AlertDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"text-sm [&_p]:leading-relaxed\", className)}\n    {...props}\n  />\n))\nAlertDescription.displayName = \"AlertDescription\"\n\nexport { Alert, AlertTitle, AlertDescription }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,IAAA,uKAAG,EACvB,6JACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,MAAM,sBAAQ,mNAAgB,CAG5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBACnC,8OAAC;QACC,KAAK;QACL,MAAK;QACL,WAAW,IAAA,yHAAE,EAAC,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGb,MAAM,WAAW,GAAG;AAEpB,MAAM,2BAAa,mNAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,IAAA,yHAAE,EAAC,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,iCAAmB,mNAAgB,CAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,IAAA,yHAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 743, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Programming/sui/meme-coin-trading/frontend/src/components/forms/CreateTokenForm.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useWallet } from '@suiet/wallet-kit';\nimport { useForm } from 'react-hook-form';\nimport { zodResolver } from '@hookform/resolvers/zod';\nimport { createTokenSchema, CreateTokenFormData } from '@/lib/validations/token';\n// Use the new V2 transaction builder that works without witness\nimport { createMemeTokenTransaction, validateTokenData, executeTransactionWithRetry } from '@/lib/sui/transactions-v2';\nimport { DEFAULT_CREATION_FEE, DEFAULT_DECIMALS, DEFAULT_TOTAL_SUPPLY } from '@/lib/constants';\nimport { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';\nimport { Input } from '@/components/ui/input';\nimport { Button } from '@/components/ui/button';\nimport { toast } from '@/hooks/use-toast';\nimport { Loader2, Coins, Info, AlertCircle } from 'lucide-react';\nimport { Alert, AlertDescription } from '@/components/ui/alert';\n\nexport function CreateTokenForm() {\n  const router = useRouter();\n  const { account, signAndExecuteTransaction } = useWallet();\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [transactionDigest, setTransactionDigest] = useState<string | null>(null);\n\n  const form = useForm({\n    resolver: zodResolver(createTokenSchema),\n    defaultValues: {\n      name: '',\n      symbol: '',\n      description: '',\n      decimals: DEFAULT_DECIMALS,\n      totalSupply: DEFAULT_TOTAL_SUPPLY,\n      imageUrl: '',\n      twitter: '',\n      website: '',\n      telegram: '',\n    },\n  });\n\n  const onSubmit = async (data: CreateTokenFormData) => {\n    if (!account?.address) {\n      toast({\n        title: 'Wallet not connected',\n        description: 'Please connect your wallet to create a token',\n        variant: 'destructive',\n      });\n      return;\n    }\n\n    setIsSubmitting(true);\n\n    try {\n      // Convert form data to the format expected by the transaction\n      const tokenData = {\n        ...data,\n        description: data.description || '',\n        imageUrl: data.imageUrl || '',\n        twitter: data.twitter || '',\n        website: data.website || '',\n        telegram: data.telegram || '',\n        totalSupply: data.totalSupply.toString(),\n      };\n\n      // Validate token data with enhanced validation\n      const validation = validateTokenData(tokenData);\n      \n      if (!validation.valid) {\n        toast({\n          title: 'Validation failed',\n          description: validation.errors[0],\n          variant: 'destructive',\n        });\n        setIsSubmitting(false);\n        return;\n      }\n\n      // Show warnings if any\n      if (validation.warnings.length > 0) {\n        console.log('Token creation warnings:', validation.warnings);\n      }\n\n      // Create the transaction using V2 entry function\n      const tx = await createMemeTokenTransaction(tokenData, account.address);\n\n      // Sign and execute with retry logic for better reliability\n      const result = await executeTransactionWithRetry(\n        tx,\n        signAndExecuteTransaction,\n        3 // max retries\n      );\n\n      if (result.digest) {\n        setTransactionDigest(result.digest);\n        toast({\n          title: 'Token created successfully!',\n          description: `Transaction: ${result.digest.slice(0, 10)}...`,\n        });\n\n        // Redirect to token details page after a short delay\n        setTimeout(() => {\n          router.push(`/token/${result.digest}`);\n        }, 2000);\n      }\n    } catch (error) {\n      console.error('Error creating token:', error);\n      \n      // Better error messages for common issues\n      let errorMessage = 'An unexpected error occurred';\n      if (error instanceof Error) {\n        if (error.message.includes('Insufficient balance')) {\n          errorMessage = 'Insufficient SUI balance for creation fee';\n        } else if (error.message.includes('User rejected')) {\n          errorMessage = 'Transaction was cancelled';\n        } else if (error.message.includes('rate limit')) {\n          errorMessage = 'Rate limit exceeded. Please try again later';\n        } else {\n          errorMessage = error.message;\n        }\n      }\n      \n      toast({\n        title: 'Failed to create token',\n        description: errorMessage,\n        variant: 'destructive',\n      });\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  const creationFeeInSui = (DEFAULT_CREATION_FEE / 1_000_000_000).toFixed(1);\n\n  return (\n    <Card className=\"w-full max-w-2xl mx-auto\">\n      <CardHeader>\n        <CardTitle className=\"flex items-center gap-2\">\n          <Coins className=\"h-6 w-6\" />\n          Create Your Meme Token\n        </CardTitle>\n        <CardDescription>\n          Launch your own token on the Sui blockchain with a bonding curve\n        </CardDescription>\n      </CardHeader>\n\n      <Form {...form}>\n        <form onSubmit={form.handleSubmit(onSubmit)}>\n          <CardContent className=\"space-y-6\">\n            {/* Basic Information */}\n            <div className=\"space-y-4\">\n              <h3 className=\"text-lg font-semibold\">Basic Information</h3>\n              \n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <FormField\n                  control={form.control}\n                  name=\"name\"\n                  render={({ field }) => (\n                    <FormItem>\n                      <FormLabel>Token Name *</FormLabel>\n                      <FormControl>\n                        <Input placeholder=\"e.g., Doge Coin\" {...field} />\n                      </FormControl>\n                      <FormDescription>\n                        The display name of your token\n                      </FormDescription>\n                      <FormMessage />\n                    </FormItem>\n                  )}\n                />\n\n                <FormField\n                  control={form.control}\n                  name=\"symbol\"\n                  render={({ field }) => (\n                    <FormItem>\n                      <FormLabel>Token Symbol *</FormLabel>\n                      <FormControl>\n                        <Input \n                          placeholder=\"e.g., DOGE\" \n                          {...field}\n                          onChange={(e) => field.onChange(e.target.value.toUpperCase())}\n                        />\n                      </FormControl>\n                      <FormDescription>\n                        The ticker symbol (uppercase)\n                      </FormDescription>\n                      <FormMessage />\n                    </FormItem>\n                  )}\n                />\n              </div>\n\n              <FormField\n                control={form.control}\n                name=\"description\"\n                render={({ field }) => (\n                  <FormItem>\n                    <FormLabel>Description</FormLabel>\n                    <FormControl>\n                      <Input \n                        placeholder=\"Describe your token's purpose and vision...\" \n                        {...field} \n                      />\n                    </FormControl>\n                    <FormDescription>\n                      A brief description of your token (optional)\n                    </FormDescription>\n                    <FormMessage />\n                  </FormItem>\n                )}\n              />\n            </div>\n\n            {/* Token Configuration */}\n            <div className=\"space-y-4\">\n              <h3 className=\"text-lg font-semibold\">Token Configuration</h3>\n              \n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <FormField\n                  control={form.control}\n                  name=\"decimals\"\n                  render={({ field }) => (\n                    <FormItem>\n                      <FormLabel>Decimals *</FormLabel>\n                      <FormControl>\n                        <Input \n                          type=\"number\" \n                          min=\"0\" \n                          max=\"18\"\n                          value={field.value as number}\n                          onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}\n                          onBlur={field.onBlur}\n                          name={field.name}\n                        />\n                      </FormControl>\n                      <FormDescription>\n                        Number of decimal places (usually 9)\n                      </FormDescription>\n                      <FormMessage />\n                    </FormItem>\n                  )}\n                />\n\n                <FormField\n                  control={form.control}\n                  name=\"totalSupply\"\n                  render={({ field }) => (\n                    <FormItem>\n                      <FormLabel>Total Supply *</FormLabel>\n                      <FormControl>\n                        <Input \n                          type=\"number\"\n                          min=\"1000\"\n                          value={field.value as number}\n                          onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}\n                          onBlur={field.onBlur}\n                          name={field.name}\n                        />\n                      </FormControl>\n                      <FormDescription>\n                        Total number of tokens to create\n                      </FormDescription>\n                      <FormMessage />\n                    </FormItem>\n                  )}\n                />\n              </div>\n            </div>\n\n            {/* Social Links */}\n            <div className=\"space-y-4\">\n              <h3 className=\"text-lg font-semibold\">Social Links (Optional)</h3>\n              \n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <FormField\n                  control={form.control}\n                  name=\"imageUrl\"\n                  render={({ field }) => (\n                    <FormItem>\n                      <FormLabel>Image URL</FormLabel>\n                      <FormControl>\n                        <Input placeholder=\"https://...\" {...field} />\n                      </FormControl>\n                      <FormMessage />\n                    </FormItem>\n                  )}\n                />\n\n                <FormField\n                  control={form.control}\n                  name=\"website\"\n                  render={({ field }) => (\n                    <FormItem>\n                      <FormLabel>Website</FormLabel>\n                      <FormControl>\n                        <Input placeholder=\"https://...\" {...field} />\n                      </FormControl>\n                      <FormMessage />\n                    </FormItem>\n                  )}\n                />\n\n                <FormField\n                  control={form.control}\n                  name=\"twitter\"\n                  render={({ field }) => (\n                    <FormItem>\n                      <FormLabel>Twitter/X</FormLabel>\n                      <FormControl>\n                        <Input placeholder=\"https://twitter.com/...\" {...field} />\n                      </FormControl>\n                      <FormMessage />\n                    </FormItem>\n                  )}\n                />\n\n                <FormField\n                  control={form.control}\n                  name=\"telegram\"\n                  render={({ field }) => (\n                    <FormItem>\n                      <FormLabel>Telegram</FormLabel>\n                      <FormControl>\n                        <Input placeholder=\"https://t.me/...\" {...field} />\n                      </FormControl>\n                      <FormMessage />\n                    </FormItem>\n                  )}\n                />\n              </div>\n            </div>\n\n            {/* Fee Information */}\n            <Alert>\n              <Info className=\"h-4 w-4\" />\n              <AlertDescription>\n                Creating a token requires a one-time fee of <strong>{creationFeeInSui} SUI</strong>\n              </AlertDescription>\n            </Alert>\n\n            {/* Transaction Result */}\n            {transactionDigest && (\n              <Alert className=\"bg-green-50 border-green-200\">\n                <AlertCircle className=\"h-4 w-4 text-green-600\" />\n                <AlertDescription className=\"text-green-800\">\n                  Token created successfully! Transaction: {transactionDigest.slice(0, 20)}...\n                </AlertDescription>\n              </Alert>\n            )}\n          </CardContent>\n\n          <CardFooter>\n            <Button \n              type=\"submit\" \n              className=\"w-full\" \n              disabled={isSubmitting}\n              size=\"lg\"\n            >\n              {isSubmitting ? (\n                <>\n                  <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n                  Creating Token...\n                </>\n              ) : (\n                <>\n                  <Coins className=\"mr-2 h-4 w-4\" />\n                  Create Token ({creationFeeInSui} SUI)\n                </>\n              )}\n            </Button>\n          </CardFooter>\n        </form>\n      </Form>\n    </Card>\n  );\n}"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,gEAAgE;AAChE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAjBA;;;;;;;;;;;;;;;;;AAmBO,SAAS;IACd,MAAM,SAAS,IAAA,+IAAS;IACxB,MAAM,EAAE,OAAO,EAAE,yBAAyB,EAAE,GAAG,IAAA,sKAAS;IACxD,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,iNAAQ,EAAC;IACjD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,IAAA,iNAAQ,EAAgB;IAE1E,MAAM,OAAO,IAAA,yKAAO,EAAC;QACnB,UAAU,IAAA,6KAAW,EAAC,uJAAiB;QACvC,eAAe;YACb,MAAM;YACN,QAAQ;YACR,aAAa;YACb,UAAU,2IAAgB;YAC1B,aAAa,+IAAoB;YACjC,UAAU;YACV,SAAS;YACT,SAAS;YACT,UAAU;QACZ;IACF;IAEA,MAAM,WAAW,OAAO;QACtB,IAAI,CAAC,SAAS,SAAS;YACrB,IAAA,qIAAK,EAAC;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;YACA;QACF;QAEA,gBAAgB;QAEhB,IAAI;YACF,8DAA8D;YAC9D,MAAM,YAAY;gBAChB,GAAG,IAAI;gBACP,aAAa,KAAK,WAAW,IAAI;gBACjC,UAAU,KAAK,QAAQ,IAAI;gBAC3B,SAAS,KAAK,OAAO,IAAI;gBACzB,SAAS,KAAK,OAAO,IAAI;gBACzB,UAAU,KAAK,QAAQ,IAAI;gBAC3B,aAAa,KAAK,WAAW,CAAC,QAAQ;YACxC;YAEA,+CAA+C;YAC/C,MAAM,aAAa,IAAA,4JAAiB,EAAC;YAErC,IAAI,CAAC,WAAW,KAAK,EAAE;gBACrB,IAAA,qIAAK,EAAC;oBACJ,OAAO;oBACP,aAAa,WAAW,MAAM,CAAC,EAAE;oBACjC,SAAS;gBACX;gBACA,gBAAgB;gBAChB;YACF;YAEA,uBAAuB;YACvB,IAAI,WAAW,QAAQ,CAAC,MAAM,GAAG,GAAG;gBAClC,QAAQ,GAAG,CAAC,4BAA4B,WAAW,QAAQ;YAC7D;YAEA,iDAAiD;YACjD,MAAM,KAAK,MAAM,IAAA,qKAA0B,EAAC,WAAW,QAAQ,OAAO;YAEtE,2DAA2D;YAC3D,MAAM,SAAS,MAAM,IAAA,sKAA2B,EAC9C,IACA,2BACA,EAAE,cAAc;;YAGlB,IAAI,OAAO,MAAM,EAAE;gBACjB,qBAAqB,OAAO,MAAM;gBAClC,IAAA,qIAAK,EAAC;oBACJ,OAAO;oBACP,aAAa,CAAC,aAAa,EAAE,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC;gBAC9D;gBAEA,qDAAqD;gBACrD,WAAW;oBACT,OAAO,IAAI,CAAC,CAAC,OAAO,EAAE,OAAO,MAAM,EAAE;gBACvC,GAAG;YACL;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YAEvC,0CAA0C;YAC1C,IAAI,eAAe;YACnB,IAAI,iBAAiB,OAAO;gBAC1B,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,yBAAyB;oBAClD,eAAe;gBACjB,OAAO,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,kBAAkB;oBAClD,eAAe;gBACjB,OAAO,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,eAAe;oBAC/C,eAAe;gBACjB,OAAO;oBACL,eAAe,MAAM,OAAO;gBAC9B;YACF;YAEA,IAAA,qIAAK,EAAC;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,mBAAmB,CAAC,+IAAoB,GAAG,aAAa,EAAE,OAAO,CAAC;IAExE,qBACE,8OAAC,wIAAI;QAAC,WAAU;;0BACd,8OAAC,8IAAU;;kCACT,8OAAC,6IAAS;wBAAC,WAAU;;0CACnB,8OAAC,6MAAK;gCAAC,WAAU;;;;;;4BAAY;;;;;;;kCAG/B,8OAAC,mJAAe;kCAAC;;;;;;;;;;;;0BAKnB,8OAAC,wIAAI;gBAAE,GAAG,IAAI;0BACZ,cAAA,8OAAC;oBAAK,UAAU,KAAK,YAAY,CAAC;;sCAChC,8OAAC,+IAAW;4BAAC,WAAU;;8CAErB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAwB;;;;;;sDAEtC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,6IAAS;oDACR,SAAS,KAAK,OAAO;oDACrB,MAAK;oDACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,4IAAQ;;8EACP,8OAAC,6IAAS;8EAAC;;;;;;8EACX,8OAAC,+IAAW;8EACV,cAAA,8OAAC,0IAAK;wEAAC,aAAY;wEAAmB,GAAG,KAAK;;;;;;;;;;;8EAEhD,8OAAC,mJAAe;8EAAC;;;;;;8EAGjB,8OAAC,+IAAW;;;;;;;;;;;;;;;;8DAKlB,8OAAC,6IAAS;oDACR,SAAS,KAAK,OAAO;oDACrB,MAAK;oDACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,4IAAQ;;8EACP,8OAAC,6IAAS;8EAAC;;;;;;8EACX,8OAAC,+IAAW;8EACV,cAAA,8OAAC,0IAAK;wEACJ,aAAY;wEACX,GAAG,KAAK;wEACT,UAAU,CAAC,IAAM,MAAM,QAAQ,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,WAAW;;;;;;;;;;;8EAG9D,8OAAC,mJAAe;8EAAC;;;;;;8EAGjB,8OAAC,+IAAW;;;;;;;;;;;;;;;;;;;;;;sDAMpB,8OAAC,6IAAS;4CACR,SAAS,KAAK,OAAO;4CACrB,MAAK;4CACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,4IAAQ;;sEACP,8OAAC,6IAAS;sEAAC;;;;;;sEACX,8OAAC,+IAAW;sEACV,cAAA,8OAAC,0IAAK;gEACJ,aAAY;gEACX,GAAG,KAAK;;;;;;;;;;;sEAGb,8OAAC,mJAAe;sEAAC;;;;;;sEAGjB,8OAAC,+IAAW;;;;;;;;;;;;;;;;;;;;;;8CAOpB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAwB;;;;;;sDAEtC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,6IAAS;oDACR,SAAS,KAAK,OAAO;oDACrB,MAAK;oDACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,4IAAQ;;8EACP,8OAAC,6IAAS;8EAAC;;;;;;8EACX,8OAAC,+IAAW;8EACV,cAAA,8OAAC,0IAAK;wEACJ,MAAK;wEACL,KAAI;wEACJ,KAAI;wEACJ,OAAO,MAAM,KAAK;wEAClB,UAAU,CAAC,IAAM,MAAM,QAAQ,CAAC,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;wEAC5D,QAAQ,MAAM,MAAM;wEACpB,MAAM,MAAM,IAAI;;;;;;;;;;;8EAGpB,8OAAC,mJAAe;8EAAC;;;;;;8EAGjB,8OAAC,+IAAW;;;;;;;;;;;;;;;;8DAKlB,8OAAC,6IAAS;oDACR,SAAS,KAAK,OAAO;oDACrB,MAAK;oDACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,4IAAQ;;8EACP,8OAAC,6IAAS;8EAAC;;;;;;8EACX,8OAAC,+IAAW;8EACV,cAAA,8OAAC,0IAAK;wEACJ,MAAK;wEACL,KAAI;wEACJ,OAAO,MAAM,KAAK;wEAClB,UAAU,CAAC,IAAM,MAAM,QAAQ,CAAC,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;wEAC5D,QAAQ,MAAM,MAAM;wEACpB,MAAM,MAAM,IAAI;;;;;;;;;;;8EAGpB,8OAAC,mJAAe;8EAAC;;;;;;8EAGjB,8OAAC,+IAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAQtB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAwB;;;;;;sDAEtC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,6IAAS;oDACR,SAAS,KAAK,OAAO;oDACrB,MAAK;oDACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,4IAAQ;;8EACP,8OAAC,6IAAS;8EAAC;;;;;;8EACX,8OAAC,+IAAW;8EACV,cAAA,8OAAC,0IAAK;wEAAC,aAAY;wEAAe,GAAG,KAAK;;;;;;;;;;;8EAE5C,8OAAC,+IAAW;;;;;;;;;;;;;;;;8DAKlB,8OAAC,6IAAS;oDACR,SAAS,KAAK,OAAO;oDACrB,MAAK;oDACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,4IAAQ;;8EACP,8OAAC,6IAAS;8EAAC;;;;;;8EACX,8OAAC,+IAAW;8EACV,cAAA,8OAAC,0IAAK;wEAAC,aAAY;wEAAe,GAAG,KAAK;;;;;;;;;;;8EAE5C,8OAAC,+IAAW;;;;;;;;;;;;;;;;8DAKlB,8OAAC,6IAAS;oDACR,SAAS,KAAK,OAAO;oDACrB,MAAK;oDACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,4IAAQ;;8EACP,8OAAC,6IAAS;8EAAC;;;;;;8EACX,8OAAC,+IAAW;8EACV,cAAA,8OAAC,0IAAK;wEAAC,aAAY;wEAA2B,GAAG,KAAK;;;;;;;;;;;8EAExD,8OAAC,+IAAW;;;;;;;;;;;;;;;;8DAKlB,8OAAC,6IAAS;oDACR,SAAS,KAAK,OAAO;oDACrB,MAAK;oDACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,4IAAQ;;8EACP,8OAAC,6IAAS;8EAAC;;;;;;8EACX,8OAAC,+IAAW;8EACV,cAAA,8OAAC,0IAAK;wEAAC,aAAY;wEAAoB,GAAG,KAAK;;;;;;;;;;;8EAEjD,8OAAC,+IAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAQtB,8OAAC,0IAAK;;sDACJ,8OAAC,0MAAI;4CAAC,WAAU;;;;;;sDAChB,8OAAC,qJAAgB;;gDAAC;8DAC4B,8OAAC;;wDAAQ;wDAAiB;;;;;;;;;;;;;;;;;;;gCAKzE,mCACC,8OAAC,0IAAK;oCAAC,WAAU;;sDACf,8OAAC,mOAAW;4CAAC,WAAU;;;;;;sDACvB,8OAAC,qJAAgB;4CAAC,WAAU;;gDAAiB;gDACD,kBAAkB,KAAK,CAAC,GAAG;gDAAI;;;;;;;;;;;;;;;;;;;sCAMjF,8OAAC,8IAAU;sCACT,cAAA,8OAAC,4IAAM;gCACL,MAAK;gCACL,WAAU;gCACV,UAAU;gCACV,MAAK;0CAEJ,6BACC;;sDACE,8OAAC,4NAAO;4CAAC,WAAU;;;;;;wCAA8B;;iEAInD;;sDACE,8OAAC,6MAAK;4CAAC,WAAU;;;;;;wCAAiB;wCACnB;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASlD", "debugId": null}}, {"offset": {"line": 1555, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Programming/sui/meme-coin-trading/frontend/src/app/create/page.tsx"], "sourcesContent": ["'use client';\n\nimport { WalletGuard } from '@/components/wallet/WalletGuard';\nimport { CreateTokenForm } from '@/components/forms/CreateTokenForm';\n\nexport default function CreateTokenPage() {\n  return (\n    <div className=\"max-w-4xl mx-auto\">\n      <div className=\"mb-8 text-center\">\n        <h1 className=\"text-3xl font-bold mb-2\">Create Your Token</h1>\n        <p className=\"text-muted-foreground\">\n          Launch your meme coin on the Sui blockchain with automatic bonding curve trading\n        </p>\n      </div>\n      \n      <WalletGuard message=\"Connect your wallet to create a token\">\n        <CreateTokenForm />\n      </WalletGuard>\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA0B;;;;;;kCACxC,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;0BAKvC,8OAAC,0JAAW;gBAAC,SAAQ;0BACnB,cAAA,8OAAC,iKAAe;;;;;;;;;;;;;;;;AAIxB", "debugId": null}}]}