{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Programming/sui/meme-coin-trading/frontend/src/components/wallet/WalletProvider.tsx"], "sourcesContent": ["'use client';\n\nimport { WalletProvider as SuietWalletProvider } from '@suiet/wallet-kit';\nimport '@suiet/wallet-kit/style.css';\nimport React from 'react';\n\ninterface WalletProviderProps {\n  children: React.ReactNode;\n}\n\nexport function WalletProvider({ children }: WalletProviderProps) {\n  return (\n    <SuietWalletProvider\n      chains={[\n        {\n          id: 'sui:devnet',\n          name: 'Su<PERSON> Devnet',\n          rpcUrl: 'https://fullnode.devnet.sui.io:443'\n        }\n      ]}\n      autoConnect={true}\n    >\n      {/* eslint-disable-next-line @typescript-eslint/no-explicit-any */}\n      {children as any}\n    </SuietWalletProvider>\n  );\n}"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;;AAUO,SAAS,eAAe,KAAiC;QAAjC,EAAE,QAAQ,EAAuB,GAAjC;IAC7B,qBACE,6LAAC,8KAAmB;QAClB,QAAQ;YACN;gBACE,IAAI;gBACJ,MAAM;gBACN,QAAQ;YACV;SACD;QACD,aAAa;kBAGZ;;;;;;AAGP;KAhBgB", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Programming/sui/meme-coin-trading/frontend/src/hooks/use-toast.ts"], "sourcesContent": ["\"use client\"\n\n// Inspired by react-hot-toast library\nimport * as React from \"react\"\n\nimport type {\n  ToastActionElement,\n  ToastProps,\n} from \"@/components/ui/toast\"\n\nconst TOAST_LIMIT = 1\nconst TOAST_REMOVE_DELAY = 1000000\n\ntype ToasterToast = ToastProps & {\n  id: string\n  title?: React.ReactNode\n  description?: React.ReactNode\n  action?: ToastActionElement\n}\n\nconst actionTypes = {\n  ADD_TOAST: \"ADD_TOAST\",\n  UPDATE_TOAST: \"UPDATE_TOAST\",\n  DISMISS_TOAST: \"DISMISS_TOAST\",\n  REMOVE_TOAST: \"REMOVE_TOAST\",\n} as const\n\nlet count = 0\n\nfunction genId() {\n  count = (count + 1) % Number.MAX_SAFE_INTEGER\n  return count.toString()\n}\n\ntype ActionType = typeof actionTypes\n\ntype Action =\n  | {\n      type: ActionType[\"ADD_TOAST\"]\n      toast: ToasterToast\n    }\n  | {\n      type: ActionType[\"UPDATE_TOAST\"]\n      toast: Partial<ToasterToast>\n    }\n  | {\n      type: ActionType[\"DISMISS_TOAST\"]\n      toastId?: ToasterToast[\"id\"]\n    }\n  | {\n      type: ActionType[\"REMOVE_TOAST\"]\n      toastId?: ToasterToast[\"id\"]\n    }\n\ninterface State {\n  toasts: ToasterToast[]\n}\n\nconst toastTimeouts = new Map<string, ReturnType<typeof setTimeout>>()\n\nconst addToRemoveQueue = (toastId: string) => {\n  if (toastTimeouts.has(toastId)) {\n    return\n  }\n\n  const timeout = setTimeout(() => {\n    toastTimeouts.delete(toastId)\n    dispatch({\n      type: \"REMOVE_TOAST\",\n      toastId: toastId,\n    })\n  }, TOAST_REMOVE_DELAY)\n\n  toastTimeouts.set(toastId, timeout)\n}\n\nexport const reducer = (state: State, action: Action): State => {\n  switch (action.type) {\n    case \"ADD_TOAST\":\n      return {\n        ...state,\n        toasts: [action.toast, ...state.toasts].slice(0, TOAST_LIMIT),\n      }\n\n    case \"UPDATE_TOAST\":\n      return {\n        ...state,\n        toasts: state.toasts.map((t) =>\n          t.id === action.toast.id ? { ...t, ...action.toast } : t\n        ),\n      }\n\n    case \"DISMISS_TOAST\": {\n      const { toastId } = action\n\n      // ! Side effects ! - This could be extracted into a dismissToast() action,\n      // but I'll keep it here for simplicity\n      if (toastId) {\n        addToRemoveQueue(toastId)\n      } else {\n        state.toasts.forEach((toast) => {\n          addToRemoveQueue(toast.id)\n        })\n      }\n\n      return {\n        ...state,\n        toasts: state.toasts.map((t) =>\n          t.id === toastId || toastId === undefined\n            ? {\n                ...t,\n                open: false,\n              }\n            : t\n        ),\n      }\n    }\n    case \"REMOVE_TOAST\":\n      if (action.toastId === undefined) {\n        return {\n          ...state,\n          toasts: [],\n        }\n      }\n      return {\n        ...state,\n        toasts: state.toasts.filter((t) => t.id !== action.toastId),\n      }\n  }\n}\n\nconst listeners: Array<(state: State) => void> = []\n\nlet memoryState: State = { toasts: [] }\n\nfunction dispatch(action: Action) {\n  memoryState = reducer(memoryState, action)\n  listeners.forEach((listener) => {\n    listener(memoryState)\n  })\n}\n\ntype Toast = Omit<ToasterToast, \"id\">\n\nfunction toast({ ...props }: Toast) {\n  const id = genId()\n\n  const update = (props: ToasterToast) =>\n    dispatch({\n      type: \"UPDATE_TOAST\",\n      toast: { ...props, id },\n    })\n  const dismiss = () => dispatch({ type: \"DISMISS_TOAST\", toastId: id })\n\n  dispatch({\n    type: \"ADD_TOAST\",\n    toast: {\n      ...props,\n      id,\n      open: true,\n      onOpenChange: (open) => {\n        if (!open) dismiss()\n      },\n    },\n  })\n\n  return {\n    id: id,\n    dismiss,\n    update,\n  }\n}\n\nfunction useToast() {\n  const [state, setState] = React.useState<State>(memoryState)\n\n  React.useEffect(() => {\n    listeners.push(setState)\n    return () => {\n      const index = listeners.indexOf(setState)\n      if (index > -1) {\n        listeners.splice(index, 1)\n      }\n    }\n  }, [state])\n\n  return {\n    ...state,\n    toast,\n    dismiss: (toastId?: string) => dispatch({ type: \"DISMISS_TOAST\", toastId }),\n  }\n}\n\nexport { useToast, toast }\n"], "names": [], "mappings": ";;;;;;;;AAEA,sCAAsC;AACtC;;AAHA;;AAUA,MAAM,cAAc;AACpB,MAAM,qBAAqB;AAS3B,MAAM,cAAc;IAClB,WAAW;IACX,cAAc;IACd,eAAe;IACf,cAAc;AAChB;AAEA,IAAI,QAAQ;AAEZ,SAAS;IACP,QAAQ,CAAC,QAAQ,CAAC,IAAI,OAAO,gBAAgB;IAC7C,OAAO,MAAM,QAAQ;AACvB;AA0BA,MAAM,gBAAgB,IAAI;AAE1B,MAAM,mBAAmB,CAAC;IACxB,IAAI,cAAc,GAAG,CAAC,UAAU;QAC9B;IACF;IAEA,MAAM,UAAU,WAAW;QACzB,cAAc,MAAM,CAAC;QACrB,SAAS;YACP,MAAM;YACN,SAAS;QACX;IACF,GAAG;IAEH,cAAc,GAAG,CAAC,SAAS;AAC7B;AAEO,MAAM,UAAU,CAAC,OAAc;IACpC,OAAQ,OAAO,IAAI;QACjB,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,QAAQ;oBAAC,OAAO,KAAK;uBAAK,MAAM,MAAM;iBAAC,CAAC,KAAK,CAAC,GAAG;YACnD;QAEF,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,QAAQ,MAAM,MAAM,CAAC,GAAG,CAAC,CAAC,IACxB,EAAE,EAAE,KAAK,OAAO,KAAK,CAAC,EAAE,GAAG;wBAAE,GAAG,CAAC;wBAAE,GAAG,OAAO,KAAK;oBAAC,IAAI;YAE3D;QAEF,KAAK;YAAiB;gBACpB,MAAM,EAAE,OAAO,EAAE,GAAG;gBAEpB,2EAA2E;gBAC3E,uCAAuC;gBACvC,IAAI,SAAS;oBACX,iBAAiB;gBACnB,OAAO;oBACL,MAAM,MAAM,CAAC,OAAO,CAAC,CAAC;wBACpB,iBAAiB,MAAM,EAAE;oBAC3B;gBACF;gBAEA,OAAO;oBACL,GAAG,KAAK;oBACR,QAAQ,MAAM,MAAM,CAAC,GAAG,CAAC,CAAC,IACxB,EAAE,EAAE,KAAK,WAAW,YAAY,YAC5B;4BACE,GAAG,CAAC;4BACJ,MAAM;wBACR,IACA;gBAER;YACF;QACA,KAAK;YACH,IAAI,OAAO,OAAO,KAAK,WAAW;gBAChC,OAAO;oBACL,GAAG,KAAK;oBACR,QAAQ,EAAE;gBACZ;YACF;YACA,OAAO;gBACL,GAAG,KAAK;gBACR,QAAQ,MAAM,MAAM,CAAC,MAAM,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,OAAO,OAAO;YAC5D;IACJ;AACF;AAEA,MAAM,YAA2C,EAAE;AAEnD,IAAI,cAAqB;IAAE,QAAQ,EAAE;AAAC;AAEtC,SAAS,SAAS,MAAc;IAC9B,cAAc,QAAQ,aAAa;IACnC,UAAU,OAAO,CAAC,CAAC;QACjB,SAAS;IACX;AACF;AAIA,SAAS,MAAM,KAAmB;QAAnB,EAAE,GAAG,OAAc,GAAnB;IACb,MAAM,KAAK;IAEX,MAAM,SAAS,CAAC,QACd,SAAS;YACP,MAAM;YACN,OAAO;gBAAE,GAAG,KAAK;gBAAE;YAAG;QACxB;IACF,MAAM,UAAU,IAAM,SAAS;YAAE,MAAM;YAAiB,SAAS;QAAG;IAEpE,SAAS;QACP,MAAM;QACN,OAAO;YACL,GAAG,KAAK;YACR;YACA,MAAM;YACN,cAAc,CAAC;gBACb,IAAI,CAAC,MAAM;YACb;QACF;IACF;IAEA,OAAO;QACL,IAAI;QACJ;QACA;IACF;AACF;AAEA,SAAS;;IACP,MAAM,CAAC,OAAO,SAAS,GAAG,yKAAc,CAAQ;IAEhD,0KAAe;8BAAC;YACd,UAAU,IAAI,CAAC;YACf;sCAAO;oBACL,MAAM,QAAQ,UAAU,OAAO,CAAC;oBAChC,IAAI,QAAQ,CAAC,GAAG;wBACd,UAAU,MAAM,CAAC,OAAO;oBAC1B;gBACF;;QACF;6BAAG;QAAC;KAAM;IAEV,OAAO;QACL,GAAG,KAAK;QACR;QACA,SAAS,CAAC,UAAqB,SAAS;gBAAE,MAAM;gBAAiB;YAAQ;IAC3E;AACF;GAlBS", "debugId": null}}, {"offset": {"line": 210, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Programming/sui/meme-coin-trading/frontend/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,IAAA,yKAAO,EAAC,IAAA,gJAAI,EAAC;AACtB", "debugId": null}}, {"offset": {"line": 231, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Programming/sui/meme-coin-trading/frontend/src/components/ui/toast.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ToastPrimitives from \"@radix-ui/react-toast\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { X } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst ToastProvider = ToastPrimitives.Provider\n\nconst ToastViewport = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Viewport>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Viewport>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Viewport\n    ref={ref}\n    className={cn(\n      \"fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]\",\n      className\n    )}\n    {...props}\n  />\n))\nToastViewport.displayName = ToastPrimitives.Viewport.displayName\n\nconst toastVariants = cva(\n  \"group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full\",\n  {\n    variants: {\n      variant: {\n        default: \"border bg-background text-foreground\",\n        destructive:\n          \"destructive group border-destructive bg-destructive text-destructive-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nconst Toast = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Root>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Root> &\n    VariantProps<typeof toastVariants>\n>(({ className, variant, ...props }, ref) => {\n  return (\n    <ToastPrimitives.Root\n      ref={ref}\n      className={cn(toastVariants({ variant }), className)}\n      {...props}\n    />\n  )\n})\nToast.displayName = ToastPrimitives.Root.displayName\n\nconst ToastAction = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Action>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Action>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Action\n    ref={ref}\n    className={cn(\n      \"inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive\",\n      className\n    )}\n    {...props}\n  />\n))\nToastAction.displayName = ToastPrimitives.Action.displayName\n\nconst ToastClose = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Close>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Close>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Close\n    ref={ref}\n    className={cn(\n      \"absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600\",\n      className\n    )}\n    toast-close=\"\"\n    {...props}\n  >\n    <X className=\"h-4 w-4\" />\n  </ToastPrimitives.Close>\n))\nToastClose.displayName = ToastPrimitives.Close.displayName\n\nconst ToastTitle = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Title>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Title>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Title\n    ref={ref}\n    className={cn(\"text-sm font-semibold\", className)}\n    {...props}\n  />\n))\nToastTitle.displayName = ToastPrimitives.Title.displayName\n\nconst ToastDescription = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Description>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Description>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Description\n    ref={ref}\n    className={cn(\"text-sm opacity-90\", className)}\n    {...props}\n  />\n))\nToastDescription.displayName = ToastPrimitives.Description.displayName\n\ntype ToastProps = React.ComponentPropsWithoutRef<typeof Toast>\n\ntype ToastActionElement = React.ReactElement<typeof ToastAction>\n\nexport {\n  type ToastProps,\n  type ToastActionElement,\n  ToastProvider,\n  ToastViewport,\n  Toast,\n  ToastTitle,\n  ToastDescription,\n  ToastClose,\n  ToastAction,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AACA;AAEA;AAPA;;;;;;;AASA,MAAM,gBAAgB,gLAAwB;AAE9C,MAAM,8BAAgB,2KAAgB,MAGpC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,gLAAwB;QACvB,KAAK;QACL,WAAW,IAAA,4HAAE,EACX,qIACA;QAED,GAAG,KAAK;;;;;;;;AAGb,cAAc,WAAW,GAAG,gLAAwB,CAAC,WAAW;AAEhE,MAAM,gBAAgB,IAAA,0KAAG,EACvB,6lBACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,MAAM,sBAAQ,2KAAgB,OAI5B,QAAmC;QAAlC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO;IACjC,qBACE,6LAAC,4KAAoB;QACnB,KAAK;QACL,WAAW,IAAA,4HAAE,EAAC,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;;AACA,MAAM,WAAW,GAAG,4KAAoB,CAAC,WAAW;AAEpD,MAAM,4BAAc,2KAAgB,OAGlC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,8KAAsB;QACrB,KAAK;QACL,WAAW,IAAA,4HAAE,EACX,sgBACA;QAED,GAAG,KAAK;;;;;;;;AAGb,YAAY,WAAW,GAAG,8KAAsB,CAAC,WAAW;AAE5D,MAAM,2BAAa,2KAAgB,OAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,6KAAqB;QACpB,KAAK;QACL,WAAW,IAAA,4HAAE,EACX,yVACA;QAEF,eAAY;QACX,GAAG,KAAK;kBAET,cAAA,6LAAC,oMAAC;YAAC,WAAU;;;;;;;;;;;;;AAGjB,WAAW,WAAW,GAAG,6KAAqB,CAAC,WAAW;AAE1D,MAAM,2BAAa,2KAAgB,OAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,6KAAqB;QACpB,KAAK;QACL,WAAW,IAAA,4HAAE,EAAC,yBAAyB;QACtC,GAAG,KAAK;;;;;;;;AAGb,WAAW,WAAW,GAAG,6KAAqB,CAAC,WAAW;AAE1D,MAAM,iCAAmB,2KAAgB,QAGvC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,mLAA2B;QAC1B,KAAK;QACL,WAAW,IAAA,4HAAE,EAAC,sBAAsB;QACnC,GAAG,KAAK;;;;;;;;AAGb,iBAAiB,WAAW,GAAG,mLAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 387, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Programming/sui/meme-coin-trading/frontend/src/components/ui/toaster.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useToast } from \"@/hooks/use-toast\"\nimport {\n  Toast,\n  ToastClose,\n  ToastDescription,\n  ToastProvider,\n  ToastTitle,\n  ToastViewport,\n} from \"@/components/ui/toast\"\n\nexport function Toaster() {\n  const { toasts } = useToast()\n\n  return (\n    <ToastProvider>\n      {toasts.map(function ({ id, title, description, action, ...props }) {\n        return (\n          <Toast key={id} {...props}>\n            <div className=\"grid gap-1\">\n              {title && <ToastTitle>{title}</ToastTitle>}\n              {description && (\n                <ToastDescription>{description}</ToastDescription>\n              )}\n            </div>\n            {action}\n            <ToastClose />\n          </Toast>\n        )\n      })}\n      <ToastViewport />\n    </ToastProvider>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;;;AAHA;;;AAYO,SAAS;;IACd,MAAM,EAAE,MAAM,EAAE,GAAG,IAAA,2IAAQ;IAE3B,qBACE,6LAAC,qJAAa;;YACX,OAAO,GAAG,CAAC,SAAU,KAA4C;oBAA5C,EAAE,EAAE,EAAE,KAAK,EAAE,WAAW,EAAE,MAAM,EAAE,GAAG,OAAO,GAA5C;gBACpB,qBACE,6LAAC,6IAAK;oBAAW,GAAG,KAAK;;sCACvB,6LAAC;4BAAI,WAAU;;gCACZ,uBAAS,6LAAC,kJAAU;8CAAE;;;;;;gCACtB,6BACC,6LAAC,wJAAgB;8CAAE;;;;;;;;;;;;wBAGtB;sCACD,6LAAC,kJAAU;;;;;;mBARD;;;;;YAWhB;0BACA,6LAAC,qJAAa;;;;;;;;;;;AAGpB;GAtBgB;;QACK,2IAAQ;;;KADb", "debugId": null}}, {"offset": {"line": 472, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Programming/sui/meme-coin-trading/frontend/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,IAAA,0KAAG,EACxB,4VACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,2KAAgB,MAC7B,QAA0D;QAAzD,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO;IACtD,MAAM,OAAO,UAAU,2KAAI,GAAG;IAC9B,qBACE,6LAAC;QACC,WAAW,IAAA,4HAAE,EAAC,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 540, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Programming/sui/meme-coin-trading/frontend/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { Check, ChevronRight, Circle } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst DropdownMenu = DropdownMenuPrimitive.Root\n\nconst DropdownMenuTrigger = DropdownMenuPrimitive.Trigger\n\nconst DropdownMenuGroup = DropdownMenuPrimitive.Group\n\nconst DropdownMenuPortal = DropdownMenuPrimitive.Portal\n\nconst DropdownMenuSub = DropdownMenuPrimitive.Sub\n\nconst DropdownMenuRadioGroup = DropdownMenuPrimitive.RadioGroup\n\nconst DropdownMenuSubTrigger = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.SubTrigger>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubTrigger> & {\n    inset?: boolean\n  }\n>(({ className, inset, children, ...props }, ref) => (\n  <DropdownMenuPrimitive.SubTrigger\n    ref={ref}\n    className={cn(\n      \"flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <ChevronRight className=\"ml-auto\" />\n  </DropdownMenuPrimitive.SubTrigger>\n))\nDropdownMenuSubTrigger.displayName =\n  DropdownMenuPrimitive.SubTrigger.displayName\n\nconst DropdownMenuSubContent = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.SubContent>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubContent>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.SubContent\n    ref={ref}\n    className={cn(\n      \"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-dropdown-menu-content-transform-origin]\",\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuSubContent.displayName =\n  DropdownMenuPrimitive.SubContent.displayName\n\nconst DropdownMenuContent = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Content>\n>(({ className, sideOffset = 4, ...props }, ref) => (\n  <DropdownMenuPrimitive.Portal>\n    <DropdownMenuPrimitive.Content\n      ref={ref}\n      sideOffset={sideOffset}\n      className={cn(\n        \"z-50 max-h-[var(--radix-dropdown-menu-content-available-height)] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-dropdown-menu-content-transform-origin]\",\n        className\n      )}\n      {...props}\n    />\n  </DropdownMenuPrimitive.Portal>\n))\nDropdownMenuContent.displayName = DropdownMenuPrimitive.Content.displayName\n\nconst DropdownMenuItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Item> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <DropdownMenuPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuItem.displayName = DropdownMenuPrimitive.Item.displayName\n\nconst DropdownMenuCheckboxItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.CheckboxItem>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.CheckboxItem>\n>(({ className, children, checked, ...props }, ref) => (\n  <DropdownMenuPrimitive.CheckboxItem\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    checked={checked}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <DropdownMenuPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </DropdownMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </DropdownMenuPrimitive.CheckboxItem>\n))\nDropdownMenuCheckboxItem.displayName =\n  DropdownMenuPrimitive.CheckboxItem.displayName\n\nconst DropdownMenuRadioItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.RadioItem>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.RadioItem>\n>(({ className, children, ...props }, ref) => (\n  <DropdownMenuPrimitive.RadioItem\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <DropdownMenuPrimitive.ItemIndicator>\n        <Circle className=\"h-2 w-2 fill-current\" />\n      </DropdownMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </DropdownMenuPrimitive.RadioItem>\n))\nDropdownMenuRadioItem.displayName = DropdownMenuPrimitive.RadioItem.displayName\n\nconst DropdownMenuLabel = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Label> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <DropdownMenuPrimitive.Label\n    ref={ref}\n    className={cn(\n      \"px-2 py-1.5 text-sm font-semibold\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuLabel.displayName = DropdownMenuPrimitive.Label.displayName\n\nconst DropdownMenuSeparator = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nDropdownMenuSeparator.displayName = DropdownMenuPrimitive.Separator.displayName\n\nconst DropdownMenuShortcut = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLSpanElement>) => {\n  return (\n    <span\n      className={cn(\"ml-auto text-xs tracking-widest opacity-60\", className)}\n      {...props}\n    />\n  )\n}\nDropdownMenuShortcut.displayName = \"DropdownMenuShortcut\"\n\nexport {\n  DropdownMenu,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuGroup,\n  DropdownMenuPortal,\n  DropdownMenuSub,\n  DropdownMenuSubContent,\n  DropdownMenuSubTrigger,\n  DropdownMenuRadioGroup,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,eAAe,uLAA0B;AAE/C,MAAM,sBAAsB,0LAA6B;AAEzD,MAAM,oBAAoB,wLAA2B;AAErD,MAAM,qBAAqB,yLAA4B;AAEvD,MAAM,kBAAkB,sLAAyB;AAEjD,MAAM,yBAAyB,6LAAgC;AAE/D,MAAM,uCAAyB,2KAAgB,MAK7C,QAA2C;QAA1C,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAO;yBACzC,6LAAC,6LAAgC;QAC/B,KAAK;QACL,WAAW,IAAA,4HAAE,EACX,0MACA,SAAS,QACT;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,yOAAY;gBAAC,WAAU;;;;;;;;;;;;;;AAG5B,uBAAuB,WAAW,GAChC,6LAAgC,CAAC,WAAW;AAE9C,MAAM,uCAAyB,2KAAgB,OAG7C,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,6LAAgC;QAC/B,KAAK;QACL,WAAW,IAAA,4HAAE,EACX,ifACA;QAED,GAAG,KAAK;;;;;;;;AAGb,uBAAuB,WAAW,GAChC,6LAAgC,CAAC,WAAW;AAE9C,MAAM,oCAAsB,2KAAgB,OAG1C,QAA0C;QAAzC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO;yBACxC,6LAAC,yLAA4B;kBAC3B,cAAA,6LAAC,0LAA6B;YAC5B,KAAK;YACL,YAAY;YACZ,WAAW,IAAA,4HAAE,EACX,+jBACA;YAED,GAAG,KAAK;;;;;;;;;;;;;AAIf,oBAAoB,WAAW,GAAG,0LAA6B,CAAC,WAAW;AAE3E,MAAM,iCAAmB,2KAAgB,OAKvC,QAAiC;QAAhC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO;yBAC/B,6LAAC,uLAA0B;QACzB,KAAK;QACL,WAAW,IAAA,4HAAE,EACX,qSACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;;;AAGb,iBAAiB,WAAW,GAAG,uLAA0B,CAAC,WAAW;AAErE,MAAM,yCAA2B,2KAAgB,OAG/C,QAA6C;QAA5C,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,OAAO;yBAC3C,6LAAC,+LAAkC;QACjC,KAAK;QACL,WAAW,IAAA,4HAAE,EACX,wOACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,gMAAmC;8BAClC,cAAA,6LAAC,gNAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGpB;;;;;;;;;AAGL,yBAAyB,WAAW,GAClC,+LAAkC,CAAC,WAAW;AAEhD,MAAM,sCAAwB,2KAAgB,QAG5C,QAAoC;QAAnC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO;yBAClC,6LAAC,4LAA+B;QAC9B,KAAK;QACL,WAAW,IAAA,4HAAE,EACX,wOACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,gMAAmC;8BAClC,cAAA,6LAAC,mNAAM;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGrB;;;;;;;;;AAGL,sBAAsB,WAAW,GAAG,4LAA+B,CAAC,WAAW;AAE/E,MAAM,kCAAoB,2KAAgB,QAKxC,QAAiC;QAAhC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO;yBAC/B,6LAAC,wLAA2B;QAC1B,KAAK;QACL,WAAW,IAAA,4HAAE,EACX,qCACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;;;AAGb,kBAAkB,WAAW,GAAG,wLAA2B,CAAC,WAAW;AAEvE,MAAM,sCAAwB,2KAAgB,QAG5C,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,4LAA+B;QAC9B,KAAK;QACL,WAAW,IAAA,4HAAE,EAAC,4BAA4B;QACzC,GAAG,KAAK;;;;;;;;AAGb,sBAAsB,WAAW,GAAG,4LAA+B,CAAC,WAAW;AAE/E,MAAM,uBAAuB;QAAC,EAC5B,SAAS,EACT,GAAG,OACmC;IACtC,qBACE,6LAAC;QACC,WAAW,IAAA,4HAAE,EAAC,8CAA8C;QAC3D,GAAG,KAAK;;;;;;AAGf;OAVM;AAWN,qBAAqB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 806, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Programming/sui/meme-coin-trading/frontend/src/lib/constants.ts"], "sourcesContent": ["// Smart Contract Addresses (Devnet) - V2 Deployment\nexport const PACKAGE_ID = \"0x12aca1986e7d431d1c0a8e65b87401a5a5753ffcb91be6c55ca914b9d7819f91\";\nexport const PLATFORM_CONFIG_V2 = \"0x77b0e8f58bc51e88e3b388c982e953d99d696e4e15e956088e0f67a993f946f7\";\nexport const TOKEN_REGISTRY_V2 = \"0xa93e991df37bc4b7c88c8e7a8f64b013a3bc38c965c5bb0a009a5bb6b014b8e8\";\nexport const BONDING_CURVE_STORAGE = \"0xcd9f4610cf2ccd6ccd85149b9350828170d0c51cb7d6f39747a6f9ea4fe898b4\";\nexport const ADMIN_CAP = \"0x4aa2df7674cbf8e27ac75689800dcb920f00365b7531a50cde4c58051d09f539\";\n\n// Legacy V1 addresses (kept for reference)\nexport const PLATFORM_CONFIG = \"0x5f09ee35e719e699eeac2ae4503bb7e965a19e996bb577e6fc36b8e88c36c6d5\";\nexport const TOKEN_REGISTRY = \"0xee5d581a48a73f03d7253cb2967d5a62d055cda3d7dbf5f2ceb329887838be0a\";\n\n// Token Creation\nexport const DEFAULT_CREATION_FEE = 100_000_000; // 0.1 SUI in MIST\nexport const DEFAULT_TRADING_FEE_BPS = 30; // 0.3%\nexport const DEFAULT_DECIMALS = 9;\nexport const DEFAULT_TOTAL_SUPPLY = 1_000_000_000;\n\n// Token Validation\nexport const MAX_NAME_LENGTH = 32;\nexport const MAX_SYMBOL_LENGTH = 10;\nexport const MAX_DESCRIPTION_LENGTH = 500;\nexport const MAX_URL_LENGTH = 200;\nexport const MAX_DECIMALS = 18;\nexport const MIN_SUPPLY = 1000;\nexport const MAX_SUPPLY = 1_000_000_000_000_000;\n\n// Network Configuration\nexport const NETWORK = process.env.NEXT_PUBLIC_NETWORK || 'devnet';\nexport const RPC_URL = process.env.NEXT_PUBLIC_RPC_URL || 'https://fullnode.devnet.sui.io:443';\n\n// UI Constants\nexport const TOAST_DURATION = 5000;\nexport const DEBOUNCE_DELAY = 300;\nexport const PAGE_SIZE = 20;"], "names": [], "mappings": "AAAA,oDAAoD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2B7B;AA1BhB,MAAM,aAAa;AACnB,MAAM,qBAAqB;AAC3B,MAAM,oBAAoB;AAC1B,MAAM,wBAAwB;AAC9B,MAAM,YAAY;AAGlB,MAAM,kBAAkB;AACxB,MAAM,iBAAiB;AAGvB,MAAM,uBAAuB,aAAa,kBAAkB;AAC5D,MAAM,0BAA0B,IAAI,OAAO;AAC3C,MAAM,mBAAmB;AACzB,MAAM,uBAAuB;AAG7B,MAAM,kBAAkB;AACxB,MAAM,oBAAoB;AAC1B,MAAM,yBAAyB;AAC/B,MAAM,iBAAiB;AACvB,MAAM,eAAe;AACrB,MAAM,aAAa;AACnB,MAAM,aAAa;AAGnB,MAAM,UAAU,2KAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI;AACnD,MAAM,UAAU,2KAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI;AAGnD,MAAM,iBAAiB;AACvB,MAAM,iBAAiB;AACvB,MAAM,YAAY", "debugId": null}}, {"offset": {"line": 886, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Programming/sui/meme-coin-trading/frontend/src/lib/sui/client.ts"], "sourcesContent": ["import { SuiClient, getFullnodeUrl } from '@mysten/sui/client';\nimport { NETWORK, RPC_URL } from '@/lib/constants';\n\n// Initialize Sui Client\nexport const suiClient = new SuiClient({\n  url: RPC_URL || getFullnodeUrl(NETWORK as 'devnet' | 'testnet' | 'mainnet')\n});\n\n// Get SUI balance for an address\nexport async function getSuiBalance(address: string): Promise<string> {\n  try {\n    const balance = await suiClient.getBalance({\n      owner: address,\n      coinType: '0x2::sui::SUI'\n    });\n    return balance.totalBalance;\n  } catch (error) {\n    console.error('Error fetching SUI balance:', error);\n    return '0';\n  }\n}\n\n// Get object details\nexport async function getObjectDetails(objectId: string) {\n  try {\n    const object = await suiClient.getObject({\n      id: objectId,\n      options: {\n        showContent: true,\n        showOwner: true,\n        showType: true\n      }\n    });\n    return object;\n  } catch (error) {\n    console.error('Error fetching object details:', error);\n    return null;\n  }\n}"], "names": [], "mappings": ";;;;;;;;AAAA;AAAA;AACA;;;AAGO,MAAM,YAAY,IAAI,kLAAS,CAAC;IACrC,KAAK,qIAAO,IAAI,IAAA,wLAAc,EAAC,qIAAO;AACxC;AAGO,eAAe,cAAc,OAAe;IACjD,IAAI;QACF,MAAM,UAAU,MAAM,UAAU,UAAU,CAAC;YACzC,OAAO;YACP,UAAU;QACZ;QACA,OAAO,QAAQ,YAAY;IAC7B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO;IACT;AACF;AAGO,eAAe,iBAAiB,QAAgB;IACrD,IAAI;QACF,MAAM,SAAS,MAAM,UAAU,SAAS,CAAC;YACvC,IAAI;YACJ,SAAS;gBACP,aAAa;gBACb,WAAW;gBACX,UAAU;YACZ;QACF;QACA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 937, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Programming/sui/meme-coin-trading/frontend/src/components/wallet/WalletConnectButton.tsx"], "sourcesContent": ["'use client';\n\nimport { useWallet } from '@suiet/wallet-kit';\nimport { Button } from '@/components/ui/button';\nimport { Wallet, LogOut, Copy, CheckCircle } from 'lucide-react';\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from '@/components/ui/dropdown-menu';\nimport { useState, useEffect } from 'react';\nimport { getSuiBalance } from '@/lib/sui/client';\n\nexport function WalletConnectButton() {\n  const { \n    connected, \n    connecting, \n    account,\n    select,\n    disconnect,\n    configuredWallets,\n    detectedWallets\n  } = useWallet();\n  \n  const [balance, setBalance] = useState('0');\n  const [copied, setCopied] = useState(false);\n  const [showWalletModal, setShowWalletModal] = useState(false);\n\n  // Fetch balance when connected\n  useEffect(() => {\n    if (connected && account?.address) {\n      getSuiBalance(account.address).then(bal => {\n        const suiAmount = (parseInt(bal) / 1_000_000_000).toFixed(4);\n        setBalance(suiAmount);\n      });\n    }\n  }, [connected, account]);\n\n  const handleCopyAddress = () => {\n    if (account?.address) {\n      navigator.clipboard.writeText(account.address);\n      setCopied(true);\n      setTimeout(() => setCopied(false), 2000);\n    }\n  };\n\n  const formatAddress = (address: string) => {\n    return `${address.slice(0, 6)}...${address.slice(-4)}`;\n  };\n\n  const handleConnect = () => {\n    setShowWalletModal(true);\n  };\n\n  const handleSelectWallet = (walletName: string) => {\n    select(walletName);\n    setShowWalletModal(false);\n  };\n\n  // If wallet is connected, show dropdown menu\n  if (connected && account) {\n    return (\n      <DropdownMenu>\n        <DropdownMenuTrigger asChild>\n          <Button variant=\"outline\" className=\"gap-2\">\n            <Wallet className=\"h-4 w-4\" />\n            {formatAddress(account.address)}\n            <span className=\"text-sm text-muted-foreground\">\n              ({balance} SUI)\n            </span>\n          </Button>\n        </DropdownMenuTrigger>\n        <DropdownMenuContent align=\"end\" className=\"w-56\">\n          <DropdownMenuLabel>Wallet</DropdownMenuLabel>\n          <DropdownMenuSeparator />\n          <DropdownMenuItem onClick={handleCopyAddress}>\n            {copied ? (\n              <>\n                <CheckCircle className=\"mr-2 h-4 w-4 text-green-500\" />\n                Copied!\n              </>\n            ) : (\n              <>\n                <Copy className=\"mr-2 h-4 w-4\" />\n                Copy Address\n              </>\n            )}\n          </DropdownMenuItem>\n          <DropdownMenuSeparator />\n          <DropdownMenuItem onClick={() => disconnect()} className=\"text-red-600\">\n            <LogOut className=\"mr-2 h-4 w-4\" />\n            Disconnect\n          </DropdownMenuItem>\n        </DropdownMenuContent>\n      </DropdownMenu>\n    );\n  }\n\n  // If wallet is not connected, show connect button and modal\n  return (\n    <>\n      <Button onClick={handleConnect} disabled={connecting} className=\"gap-2\">\n        <Wallet className=\"h-4 w-4\" />\n        {connecting ? 'Connecting...' : 'Connect Wallet'}\n      </Button>\n\n      {/* Wallet Selection Modal */}\n      {showWalletModal && (\n        <div className=\"fixed inset-0 z-50 flex items-center justify-center\">\n          <div \n            className=\"absolute inset-0 bg-black/50\"\n            onClick={() => setShowWalletModal(false)}\n          />\n          <div className=\"relative bg-white dark:bg-gray-900 rounded-lg p-6 w-full max-w-md\">\n            <h2 className=\"text-xl font-semibold mb-4\">Select a Wallet</h2>\n            <div className=\"space-y-2\">\n              {[...configuredWallets, ...detectedWallets].map((wallet) => (\n                <Button\n                  key={wallet.name}\n                  variant=\"outline\"\n                  className=\"w-full justify-start gap-3\"\n                  onClick={() => handleSelectWallet(wallet.name)}\n                  disabled={!wallet.installed}\n                >\n                  <img \n                    src={wallet.iconUrl} \n                    alt={wallet.name}\n                    className=\"h-6 w-6\"\n                  />\n                  <span>{wallet.name}</span>\n                  {!wallet.installed && (\n                    <span className=\"ml-auto text-sm text-muted-foreground\">\n                      Not installed\n                    </span>\n                  )}\n                </Button>\n              ))}\n            </div>\n            <Button\n              variant=\"ghost\"\n              className=\"w-full mt-4\"\n              onClick={() => setShowWalletModal(false)}\n            >\n              Cancel\n            </Button>\n          </div>\n        </div>\n      )}\n    </>\n  );\n}"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAQA;AACA;;;AAdA;;;;;;;AAgBO,SAAS;;IACd,MAAM,EACJ,SAAS,EACT,UAAU,EACV,OAAO,EACP,MAAM,EACN,UAAU,EACV,iBAAiB,EACjB,eAAe,EAChB,GAAG,IAAA,yKAAS;IAEb,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,yKAAQ,EAAC;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,yKAAQ,EAAC;IACrC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,yKAAQ,EAAC;IAEvD,+BAA+B;IAC/B,IAAA,0KAAS;yCAAC;YACR,IAAI,cAAa,oBAAA,8BAAA,QAAS,OAAO,GAAE;gBACjC,IAAA,+IAAa,EAAC,QAAQ,OAAO,EAAE,IAAI;qDAAC,CAAA;wBAClC,MAAM,YAAY,CAAC,SAAS,OAAO,aAAa,EAAE,OAAO,CAAC;wBAC1D,WAAW;oBACb;;YACF;QACF;wCAAG;QAAC;QAAW;KAAQ;IAEvB,MAAM,oBAAoB;QACxB,IAAI,oBAAA,8BAAA,QAAS,OAAO,EAAE;YACpB,UAAU,SAAS,CAAC,SAAS,CAAC,QAAQ,OAAO;YAC7C,UAAU;YACV,WAAW,IAAM,UAAU,QAAQ;QACrC;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAO,AAAC,GAA2B,OAAzB,QAAQ,KAAK,CAAC,GAAG,IAAG,OAAuB,OAAlB,QAAQ,KAAK,CAAC,CAAC;IACpD;IAEA,MAAM,gBAAgB;QACpB,mBAAmB;IACrB;IAEA,MAAM,qBAAqB,CAAC;QAC1B,OAAO;QACP,mBAAmB;IACrB;IAEA,6CAA6C;IAC7C,IAAI,aAAa,SAAS;QACxB,qBACE,6LAAC,+JAAY;;8BACX,6LAAC,sKAAmB;oBAAC,OAAO;8BAC1B,cAAA,6LAAC,+IAAM;wBAAC,SAAQ;wBAAU,WAAU;;0CAClC,6LAAC,mNAAM;gCAAC,WAAU;;;;;;4BACjB,cAAc,QAAQ,OAAO;0CAC9B,6LAAC;gCAAK,WAAU;;oCAAgC;oCAC5C;oCAAQ;;;;;;;;;;;;;;;;;;8BAIhB,6LAAC,sKAAmB;oBAAC,OAAM;oBAAM,WAAU;;sCACzC,6LAAC,oKAAiB;sCAAC;;;;;;sCACnB,6LAAC,wKAAqB;;;;;sCACtB,6LAAC,mKAAgB;4BAAC,SAAS;sCACxB,uBACC;;kDACE,6LAAC,6OAAW;wCAAC,WAAU;;;;;;oCAAgC;;6DAIzD;;kDACE,6LAAC,6MAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;sCAKvC,6LAAC,wKAAqB;;;;;sCACtB,6LAAC,mKAAgB;4BAAC,SAAS,IAAM;4BAAc,WAAU;;8CACvD,6LAAC,uNAAM;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;;IAM7C;IAEA,4DAA4D;IAC5D,qBACE;;0BACE,6LAAC,+IAAM;gBAAC,SAAS;gBAAe,UAAU;gBAAY,WAAU;;kCAC9D,6LAAC,mNAAM;wBAAC,WAAU;;;;;;oBACjB,aAAa,kBAAkB;;;;;;;YAIjC,iCACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,WAAU;wBACV,SAAS,IAAM,mBAAmB;;;;;;kCAEpC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA6B;;;;;;0CAC3C,6LAAC;gCAAI,WAAU;0CACZ;uCAAI;uCAAsB;iCAAgB,CAAC,GAAG,CAAC,CAAC,uBAC/C,6LAAC,+IAAM;wCAEL,SAAQ;wCACR,WAAU;wCACV,SAAS,IAAM,mBAAmB,OAAO,IAAI;wCAC7C,UAAU,CAAC,OAAO,SAAS;;0DAE3B,6LAAC;gDACC,KAAK,OAAO,OAAO;gDACnB,KAAK,OAAO,IAAI;gDAChB,WAAU;;;;;;0DAEZ,6LAAC;0DAAM,OAAO,IAAI;;;;;;4CACjB,CAAC,OAAO,SAAS,kBAChB,6LAAC;gDAAK,WAAU;0DAAwC;;;;;;;uCAbrD,OAAO,IAAI;;;;;;;;;;0CAoBtB,6LAAC,+IAAM;gCACL,SAAQ;gCACR,WAAU;gCACV,SAAS,IAAM,mBAAmB;0CACnC;;;;;;;;;;;;;;;;;;;;AAQb;GAzIgB;;QASV,yKAAS;;;KATC", "debugId": null}}]}